package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.util.CsvContentComparator;
import com.ms.bp.util.TestDataManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.http.AbortableInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class IndirectProfitMakerImportServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterImportServiceTest.class);
    private DataApplicationService dataApplicationService;
    // Mock オブジェクト
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;
    @Mock
    private S3Service mockS3Service;
    @Mock
    private Context mockLambdaContext;
    private UserInfo testUserInfo;
    private ImportRequest testImportRequest;
    // エクスポート用Excel テストデータ管理器
    private TestDataManager testDataManager;
    // 挿入されたテストデータの追跡情報
    private Map<String, List<Map<String, Object>>> insertedDataTracker;
    private final String prefix_folder = "test-import-output-indirectprofitmaker_";
    private final String prefix_file = "error_間接利益計画_メーカー別";
    // MockedConstruction for S3Service
    private MockedConstruction<S3Service> mockedS3ServiceConstruction;

    /**
     * ケース毎実施前作業
     */
    @BeforeEach
    @SuppressWarnings("unchecked")
    void setUp() {
        logger.info("=== importTask集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // MockedConstruction for S3Service - DataApplicationService作成前に設定
            setupS3ServiceMockedConstruction();

            // テスト対象
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // Mock の基本設定
            setupMockBehaviors();

            String insertDataPath = "indirectprofitmakerimport/insertdata/indirectprofitmakerimport_import_insert_test_data_1.xlsx";
            testDataManager = new TestDataManager(insertDataPath);
            insertedDataTracker = testDataManager.insertAllTestData();

            logger.info("=== importTask集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("テストセットアップに失敗しました", e);
        }

        // テスト用ユーザー情報を作成
        testUserInfo = createTestUserInfo();

        // テスト用インポートリクエストを作成
        testImportRequest = createTestImportRequest();
    }

    /**
     * ケース毎実施後作業
     */
    @AfterEach
    void tearDown() {
        logger.info("=== 集成テストクリーンアップ開始 ===");

        // 挿入したテストデータを削除
        if (insertedDataTracker != null) {
            testDataManager.deleteAllTestData(insertedDataTracker);
        }

        // MockedConstruction をクリーンアップ
        if (mockedS3ServiceConstruction != null) {
            mockedS3ServiceConstruction.close();
            logger.debug("MockedConstruction<S3Service> をクリーンアップしました");
        }

        logger.info("=== 集成テストクリーンアップ完了 ===");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：パータン1: ヘッダ数不足
     * パータン2: ヘッダ名不正
     * 確認内容：下記異常が発生
     * ERR_010: アップロードされたファイルは{0}の形式ではありません。内容をご確認ください。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]のヘッダ不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_1.csv",// ヘッダ数不足
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_2.csv" // ヘッダ名不正
    })
    @Order(1)
    void testExecuteImportTask_ヘッダチェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "アップロードされたファイルは間接利益計画_メーカー別の形式ではありません。内容をご確認ください。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：システム管理者、且ファイル内のグループコードに対するエリアCDが不正で設定
     * data1:ファイル内のグループコードから取得したグループマスタ.エリアコード：存在しない
     * data2:ファイル内のグループコードから取得したグループマスタ.エリアコード：日付不正で設定
     * data3:ファイル内のグループコードから取得したグループマスタ.エリアコード：使用禁止区分が１で設定
     * data4:ファイル内のグループコードから取得したグループマスタ.エリアコード：権限エリアに存在しない
     * 確認内容：下記異常が発生
     * 権限チェック
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(2)
    void testExecuteImportTask_権限チェック_グループコーのチェック_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_3.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：グループコード、エラー内容：登録権限のない値が入力されています。",
                "行：2、項目：グループコード、エラー内容：登録権限のない値が入力されています。",
                "行：3、項目：グループコード、エラー内容：登録権限のない値が入力されています。",
                "行：4、項目：グループコード、エラー内容：登録権限のない値が入力されています。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：権限エリアパターンに「1:エリアパターン1（本社相当権限）」
     * data1:本社場所区分が「1:場所」で設定
     * 確認内容：下記異常が発生
     * 権限チェック
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(2)
    void testExecuteImportTask_権限チェック_本社_場所区分のチェック_本社_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_4.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：本社場所区分、エラー内容：登録権限のない値が入力されています。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：権限エリアパターンに「2:エリアパターン2（自エリアのみに権限を持つ）」
     * data1:本社場所区分が「 0:本社」で設定
     * 確認内容：下記異常が発生
     * 権限チェック
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(2)
    void testExecuteImportTask_権限チェック_本社_場所区分のチェック_自エリア_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_5.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        testImportRequest.setAreaCode("9801");
        testUserInfo.setShainCode("0NED02");
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：本社場所区分、エラー内容：登録権限のない値が入力されています。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記必須項目を設定しない
     * ・カテゴリコード
     * ・本社場所区分
     * ・メーカーコード
     * ・メーカー別管理No.
     * ・グループコード
     * ・グループコード
     * ・在直区分
     * 確認内容：下記異常が発生
     * 必須チェック
     * ERR_016: 行：{0}、項目：{1}、エラー内容：必須チェックエラー
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(3)
    void testExecuteImportTask_必須チェック_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_6.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：カテゴリコード、エラー内容：必須チェックエラー",
                "行：1、項目：本社場所区分、エラー内容：必須チェックエラー",
                "行：1、項目：メーカーコード、エラー内容：必須チェックエラー",
                "行：1、項目：メーカー別管理No.、エラー内容：必須チェックエラー",
                "行：1、項目：グループコード、エラー内容：必須チェックエラー",
                "行：1、項目：未収区分、エラー内容：必須チェックエラー",
                "行：1、項目：在直区分、エラー内容：必須チェックエラー"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記項目を桁数不正に設定
     * ・カテゴリコード: 最小が1 最大が1
     * ・本社場所区分: 最小が1 最大が1
     * ・メーカーコード: 最小が7 最大が7
     * ・メーカー別管理No.: 最小が1 最大が4
     * ・採算管理単位コード: 最小が7 最大が7
     * ・会社コード: 最小が6 最大が6
     * ・エリアコード: 最小が4 最大が4
     * ・サブエリアコード: 最小が4 最大が4
     * ・グループコード: 最小が4 最大が4
     * ・未収区分: 最小が1 最大が1
     * ・在直区分: 最小が1 最大が1
     * ・4月: 最小が1 最大が10
     * ・5月: 最小が1 最大が10
     * ・6月: 最小が1 最大が10
     * ・7月: 最小が1 最大が10
     * ・8月: 最小が1 最大が10
     * ・9月: 最小が1 最大が10
     * ・10月: 最小が1 最大が10
     * ・11月: 最小が1 最大が10
     * ・12月: 最小が1 最大が10
     * ・1月: 最小が1 最大が10
     * ・2月: 最小が1 最大が10
     * ・3月: 最小が1 最大が10
     * 確認内容：下記異常が発生
     * 桁数エラー:
     * ERR_017: 行：{0}、項目：{1}、エラー内容：桁数エラー。{2}桁から{3}桁の間で入力してください。
     * 権限チェック
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * コード存在チェック
     * ERR_031: 行：{0}、項目：{1}、エラー内容：値チェックエラー。{2}で入力してください。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]の桁数不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_7.csv" ,// 桁数未満
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_8.csv" // 桁数超える
    })
    @Order(4)
    void testExecuteImportTask_桁数チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);

        // 期待エラー内容
        List<String> expectContents = null;
        if (uploadFilePath.contains("7")) {
            expectContents =new ArrayList<>(List.of(
                    "行：1、項目：メーカーコード、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：採算管理単位コード、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：会社コード、エラー内容：桁数エラー。6桁から6桁の間で入力してください。",
                    "行：1、項目：エリアコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：サブエリアコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：グループコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：グループコード、エラー内容：登録権限のない値が入力されています。",
                    "行：1、メーカーコード:999990はメーカーマスタに存在しません。",
                    "行：1、採算管理単位コード:999900はエリア＿見通し・計画_採算管理単位C別に存在しません。"
            ));
        } else {
            expectContents = new ArrayList<>(List.of(
                    "行：1、項目：カテゴリコード、エラー内容：桁数エラー。1桁から1桁の間で入力してください。",
                    "行：1、項目：カテゴリコード、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                    "行：1、項目：本社場所区分、エラー内容：桁数エラー。1桁から1桁の間で入力してください。",
                    "行：1、項目：メーカーコード、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：メーカー別管理No.、エラー内容：桁数エラー。1桁から4桁の間で入力してください。",
                    "行：1、項目：採算管理単位コード、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：会社コード、エラー内容：桁数エラー。6桁から6桁の間で入力してください。",
                    "行：1、項目：エリアコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：サブエリアコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：グループコード、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：未収区分、エラー内容：桁数エラー。1桁から1桁の間で入力してください。",
                    "行：1、項目：未収区分、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                    "行：1、項目：在直区分、エラー内容：桁数エラー。1桁から1桁の間で入力してください。",
                    "行：1、項目：在直区分、エラー内容：値チェックエラー。1、3で入力してください。",
                    "行：1、項目：4月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：5月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：6月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：7月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：8月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：9月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：10月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：11月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：12月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：1月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：2月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：3月、エラー内容：桁数エラー。1桁から10桁の間で入力してください。",
                    "行：1、項目：グループコード、エラー内容：登録権限のない値が入力されています。",
                    "行：1、メーカーコード:99999011はメーカーマスタに存在しません。",
                    "行：1、採算管理単位コード:99990011はエリア＿見通し・計画_採算管理単位C別に存在しません。"
            ));
        }
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記項目を正しく設定しない
     * ・本社場所区分: 半角数字
     * ・メーカーコード: 半角数字
     * ・採算管理単位コード: 半角数字
     * ・会社コード: 半角数字
     * ・エリアコード: 半角数字
     * ・サブエリアコード: 半角数字
     * ・グループコード: 半角数字
     * ・未収区分: 半角数字
     * ・在直区分: 半角数字
     * ・4月: 半角数字
     * ・5月: 半角数字
     * ・6月: 半角数字
     * ・7月: 半角数字
     * ・8月: 半角数字
     * ・9月: 半角数字
     * ・10月: 半角数字
     * ・11月: 半角数字
     * ・12月: 半角数字
     * ・1月: 半角数字
     * ・2月: 半角数字
     * ・3月: 半角数字
     * 確認内容：下記異常が発生
     * 書式チェック
     * ERR_018: 行：{0}、項目：{1}、エラー内容：書式エラー。{2}で入力してください。
     * 値チェック
     * ERR_031: 行：{0}、項目：{1}、エラー内容：値チェックエラー。{2}で入力してください。
     * 権限チェック
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * コード存在チェック
     * ERR_031: 行：{0}、項目：{1}、エラー内容：値チェックエラー。{2}で入力してください。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]の書式不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_9.csv" ,// 半角英字
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_10.csv" , // 全角数字
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_11.csv" , // 記号(!@#$%^&*_)
    })
    @Order(6)
    void testExecuteImportTask_書式チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("1");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：カテゴリコード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：カテゴリコード、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                "行：1、項目：本社場所区分、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：メーカーコード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：メーカー別管理No.、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：採算管理単位コード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：会社コード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：エリアコード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：サブエリアコード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：グループコード、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：未収区分、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：未収区分、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                "行：1、項目：在直区分、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：在直区分、エラー内容：値チェックエラー。1、3で入力してください。",
                "行：1、項目：4月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：5月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：6月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：7月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：8月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：9月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：10月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：11月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：12月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：1月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：2月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：3月、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：グループコード、エラー内容：登録権限のない値が入力されています。"
        ));

        if (uploadFilePath.contains("9")) {
            expectContents.addAll(List.of(
                    "行：1、メーカーコード:abcdefgはメーカーマスタに存在しません。",
                    "行：1、採算管理単位コード:abcdefgはエリア＿見通し・計画_採算管理単位C別に存在しません。"));

        } else if (uploadFilePath.contains("10")) {
            expectContents.addAll(List.of(
                    "行：1、メーカーコード:２２２２２２２はメーカーマスタに存在しません。",
                    "行：1、採算管理単位コード:２２２２２２２はエリア＿見通し・計画_採算管理単位C別に存在しません。"));

        } else {
            expectContents.addAll(List.of(
                    "行：1、メーカーコード:$%^&*()はメーカーマスタに存在しません。",
                    "行：1、採算管理単位コード:$%^&*()はエリア＿見通し・計画_採算管理単位C別に存在しません。"));
        }
        expectContents.addAll(0, getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記必須項目を設定しない
     * ・カテゴリコード: 1or2or3or4
     * ・未収区分: 1or2or3or4
     * ・在直区分: 1or3
     * 確認内容：下記異常が発生
     * 値チェック
     * ERR_031: 行：{0}、項目：{1}、エラー内容：値チェックエラー。{2}で入力してください。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(3)
    void testExecuteImportTask_値チェック_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_12.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、項目：カテゴリコード、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                "行：1、項目：未収区分、エラー内容：値チェックエラー。1、2、3、4で入力してください。",
                "行：1、項目：在直区分、エラー内容：値チェックエラー。1、3で入力してください。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記必須項目を設定しない
     * ・data1: メーカーマスタに存在しない
     * ・data2: メーカーマスタに使用禁止区分 !='0'
     * 確認内容：下記異常が発生
     * DB存在性チェック
     * ERR_030: 行：{0}、{1}:{2}は{3}に存在しません。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(3)
    void testExecuteImportTask_メーカーコードの存在チェック_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_13.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        DateUtil.setTestFixedDateTime("9999");

        // 期待エラー内容
        List<String> expectContents = new ArrayList<>(List.of(
                "行：1、メーカーコード:9899901はメーカーマスタに存在しません。",
                "行：2、メーカーコード:9799901はメーカーマスタに存在しません。"
        ));
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：下記必須項目を設定しない
     * ・data1: エリア＿見通し・計画_採算管理単位C別、間接利益計画_メーカー別に採算管理単位コードが存在しない
     * ・data2: エリア＿見通し・計画_採算管理単位C別、間接利益計画_メーカー別にグループコードが存在しない
     * ・data3: エリア＿見通し・計画_採算管理単位C別、間接利益計画_メーカー別に年度に対するデータが存在しない
     * 確認内容：下記異常が発生
     * DB存在性チェック
     * ERR_030: 行：{0}、{1}:{2}は{3}に存在しません。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "DB存在チェックテスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_14.csv",// data1、data2
            "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_15.csv" // data3
    })
    void testExecuteImportTask_採算管理単位コードとグループコードの存在チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");

        // 期待エラー内容
        List<String> expectContents = null;

        if (uploadFilePath.contains("14")) {
            DateUtil.setTestFixedDateTime("9999");
           expectContents = new ArrayList<>(List.of(
                    "行：1、採算管理単位コード:9999001はエリア＿見通し・計画_採算管理単位C別に存在しません。",
                    "行：2、採算管理単位コード:9899001はエリア＿見通し・計画_採算管理単位C別に存在しません。"
            ));
        } else{
            DateUtil.setTestFixedDateTime("9899");
            expectContents = new ArrayList<>(List.of(
                    "行：1、採算管理単位コード:9999001はエリア＿見通し・計画_採算管理単位C別に存在しません。"
            ));
        }
        expectContents.addAll(0,getHeadOfFile());

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：複数行に対していろいろな不正な設定
     * 且、100以上のチェックエラーがある
     * 確認内容：下記異常が発生
     * エラーファイルに100件のみエラーがある
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(8)
    void testExecuteImportTask_100件エラー以上_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_16.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertThat(actualContents.stream().skip(4)).hasSize(100);
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：csvに記号「"」を単独で設定
     * 確認内容：履歴のステータス: システムエラー
     */
    @Test
    @Order(9)
    void testExecuteImportTask_システムエラー_異常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_17.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);

        testImportRequest.setSystemAdminFlag("1");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE);
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：複数件登録(全項目)
     * data1:新規、すべての項目が満桁数で設定(半角で設定、できるのは先頭は「0」で設定)
     * data2:新規、すべての項目が満桁数で設定(数字以外は全角で設定)
     * data3:新規、必須項目のみ
     * 確認内容：新規データがアップロードファイルの内容と一致する
     * 履歴のステータス: 完了
     */
    @Test
    @Order(10)
    void testExecuteImportTask_新規追加_正常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_18.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("1");
        DateUtil.setTestFixedDateTime("9999");

        DateUtil.setTestFixedDateTime("9999");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // 新規データ検証
        List<riekiKkkmaker> results = getDatabaseData("9999", List.of("0999901", "9999902", "9999903"));
        assertThat(results).hasSize(3);

        assertEquals("9999", results.get(0).nendo());
        assertEquals("0999901", results.get(0).makerCode());
        assertEquals(BigDecimal.ZERO, results.get(0).makerKanriNo());
        assertEquals("1", results.get(0).ctgryCode());
        assertEquals("0999", results.get(0).groupCode());
        assertEquals("0", results.get(0).hnshBashoKubun());
        assertEquals("1", results.get(0).zaichokuKubun());
        assertEquals("1", results.get(0).mishuKubun());
        assertEquals("0999001", results.get(0).ssnknTncd());
        assertEquals("***********234567890***********234567890***********234567890***********2345", results.get(0).ssnKanriTnmKanji());
        assertEquals("099998", results.get(0).systmUnyoKigyoCode());
        assertEquals("***********234567890***********23456789012345", results.get(0).systmUnyoKgymKanji());
        assertEquals("0998", results.get(0).areaCode());
        assertEquals("***********2345678901234567891", results.get(0).areaMeiKanji());
        assertEquals("0351", results.get(0).subAreaCode());
        assertEquals("***********2345678901234567892", results.get(0).subAreaMeiKanji());
        assertEquals("***********2345678901234567890", results.get(0).groupMeiKanji());
        assertNull(results.get(0).unitCode());
        assertNull( results.get(0).unitMeiKanji());
        assertEquals("企画未収", results.get(0).mishuMei());
        assertEquals("在庫", results.get(0).zaikoChks());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk1Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk2Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk3Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk4Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk5Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk6Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk7Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk8Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk9Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk10Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk11Tskm());
        assertEquals(BigDecimal.ZERO, results.get(0).knstsRiekiKkk12Tskm());
        assertEquals("IMGBAK_006", results.get(0).trkPrgrmId());
        assertEquals("900001", results.get(0).trkSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(0).trkShainCode());
        assertEquals("IMGBAK_006", results.get(0).kshnPrgrmId());
        assertEquals("900001", results.get(0).kshnSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(0).kshnShainCode());
        assertEquals(1, results.get(0).vrsn());
        assertNotNull(results.get(0).rcrdTrkNchj());
        assertNotNull(results.get(0).rcrdKshnNchj());

        assertEquals("9999", results.get(1).nendo());
        assertEquals("9999902", results.get(1).makerCode());
        assertEquals(BigDecimal.valueOf(1002), results.get(1).makerKanriNo());
        assertEquals("2", results.get(1).ctgryCode());
        assertEquals("9910", results.get(1).groupCode());
        assertEquals("1", results.get(1).hnshBashoKubun());
        assertEquals("3", results.get(1).zaichokuKubun());
        assertEquals("2", results.get(1).mishuKubun());
        assertEquals("9999002", results.get(1).ssnknTncd());
        assertEquals("エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算エリア採算", results.get(1).ssnKanriTnmKanji());
        assertEquals("099996", results.get(1).systmUnyoKigyoCode());
        assertEquals("システムこグループこグループこグループこグループこグループこグループこグループこグループグ", results.get(1).systmUnyoKgymKanji());
        assertEquals("9800", results.get(1).areaCode());
        assertEquals("エリーアこグループこグループこグループこグループこグループグ", results.get(1).areaMeiKanji());
        assertEquals("0351", results.get(1).subAreaCode());
        assertEquals("エリーアこグループこグループこグループこグループこグループー", results.get(1).subAreaMeiKanji());
        assertEquals("グループこグループこグループこグループこグループこグループル", results.get(1).groupMeiKanji());
        assertNull(results.get(1).unitCode());
        assertNull( results.get(1).unitMeiKanji());
        assertEquals("特別未収", results.get(1).mishuMei());
        assertEquals("直送", results.get(1).zaikoChks());
        assertEquals(BigDecimal.valueOf(1234567891*1000L), results.get(1).knstsRiekiKkk1Tskm());
        assertEquals(BigDecimal.valueOf(1234567892*1000L), results.get(1).knstsRiekiKkk2Tskm());
        assertEquals(BigDecimal.valueOf(1234567893*1000L), results.get(1).knstsRiekiKkk3Tskm());
        assertEquals(BigDecimal.valueOf(1234567894*1000L), results.get(1).knstsRiekiKkk4Tskm());
        assertEquals(BigDecimal.valueOf(1234567895*1000L), results.get(1).knstsRiekiKkk5Tskm());
        assertEquals(BigDecimal.valueOf(1234567896*1000L), results.get(1).knstsRiekiKkk6Tskm());
        assertEquals(BigDecimal.valueOf(1234567897*1000L), results.get(1).knstsRiekiKkk7Tskm());
        assertEquals(BigDecimal.valueOf(1234567898*1000L), results.get(1).knstsRiekiKkk8Tskm());
        assertEquals(BigDecimal.valueOf(1234567899*1000L), results.get(1).knstsRiekiKkk9Tskm());
        assertEquals(BigDecimal.valueOf(1234567900*1000L), results.get(1).knstsRiekiKkk10Tskm());
        assertEquals(BigDecimal.valueOf(1234567901*1000L), results.get(1).knstsRiekiKkk11Tskm());
        assertEquals(BigDecimal.valueOf(1234567902*1000L), results.get(1).knstsRiekiKkk12Tskm());
        assertEquals("IMGBAK_006", results.get(1).trkPrgrmId());
        assertEquals("900001", results.get(1).trkSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(1).trkShainCode());
        assertEquals("IMGBAK_006", results.get(1).kshnPrgrmId());
        assertEquals("900001", results.get(1).kshnSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(1).kshnShainCode());
        assertEquals(1, results.get(1).vrsn());
        assertNotNull(results.get(1).rcrdTrkNchj());
        assertNotNull(results.get(1).rcrdKshnNchj());

        assertEquals("9999", results.get(2).nendo());
        assertEquals("9999903", results.get(2).makerCode());
        assertEquals(BigDecimal.valueOf(1003), results.get(2).makerKanriNo());
        assertEquals("3", results.get(2).ctgryCode());
        assertEquals("9909", results.get(2).groupCode());
        assertEquals("1", results.get(2).hnshBashoKubun());
        assertEquals("1", results.get(2).zaichokuKubun());
        assertEquals("3", results.get(2).mishuKubun());
        assertEquals("", results.get(2).ssnknTncd());
        assertNull( results.get(2).ssnKanriTnmKanji());
        assertEquals("099997", results.get(2).systmUnyoKigyoCode());
        assertNull( results.get(2).systmUnyoKgymKanji());
        assertEquals("9801", results.get(2).areaCode());
        assertNull( results.get(2).areaMeiKanji());
        assertEquals("0351", results.get(2).subAreaCode());
        assertNull( results.get(2).subAreaMeiKanji());
        assertEquals("グループこグループこグループこグループこグループこグループこ", results.get(2).groupMeiKanji());
        assertNull(results.get(2).unitCode());
        assertNull( results.get(2).unitMeiKanji());
        assertEquals("年契未収", results.get(2).mishuMei());
        assertEquals("在庫", results.get(2).zaikoChks());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk1Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk2Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk3Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk4Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk5Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk6Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk7Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk8Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk9Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk10Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk11Tskm());
        assertEquals(BigDecimal.ZERO, results.get(2).knstsRiekiKkk12Tskm());
        assertEquals("IMGBAK_006", results.get(2).trkPrgrmId());
        assertEquals("900001", results.get(2).trkSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(2).trkShainCode());
        assertEquals("IMGBAK_006", results.get(2).kshnPrgrmId());
        assertEquals("900001", results.get(2).kshnSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(2).kshnShainCode());
        assertEquals(1, results.get(2).vrsn());
        assertNotNull(results.get(2).rcrdTrkNchj());
        assertNotNull(results.get(2).rcrdKshnNchj());
        // 新規追加データを削除しなくて、後続の更新ケースで利用必要
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：複数件更新(全項目)、1件新規
     * data1:更新、すべての項目が満桁数で設定
     * data2:新規、すべての項目が満桁数で設定(数字以外は全角で設定) 次年度計画マスタ:採算管理単位コーが存在、グループコードが不存在
     * 確認内容：新規データがアップロードファイルの内容と一致する
     * 履歴のステータス: 完了
     */
    @Test
    @Order(10)
    void testExecuteImportTask_更新_新規_正常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_19.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        try {
        DateUtil.setTestFixedDateTime("9999");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // 新規データ検証
        List<riekiKkkmaker> results = getDatabaseData("9999", List.of("0999901", "9999904"));
        assertThat(results).hasSize(2);

        assertEquals("9999", results.get(0).nendo());
        assertEquals("0999901", results.get(0).makerCode());
        assertEquals(BigDecimal.ZERO, results.get(0).makerKanriNo());
        assertEquals("1", results.get(0).ctgryCode());
        assertEquals("0999", results.get(0).groupCode());
        assertEquals("0", results.get(0).hnshBashoKubun());
        assertEquals("1", results.get(0).zaichokuKubun());
        assertEquals("2", results.get(0).mishuKubun());
        assertEquals("1118888", results.get(0).ssnknTncd());
        assertEquals("特殊採算77", results.get(0).ssnKanriTnmKanji());
        assertEquals("099998", results.get(0).systmUnyoKigyoCode());
        assertEquals("***********234567890***********23456789012345", results.get(0).systmUnyoKgymKanji());
        assertEquals("0998", results.get(0).areaCode());
        assertEquals("***********2345678901234567891", results.get(0).areaMeiKanji());
        assertEquals("0351", results.get(0).subAreaCode());
        assertEquals("***********2345678901234567892", results.get(0).subAreaMeiKanji());
        assertEquals("***********2345678901234567890", results.get(0).groupMeiKanji());
        assertNull(results.get(0).unitCode());
        assertNull( results.get(0).unitMeiKanji());
        assertEquals("特別未収", results.get(0).mishuMei());
        assertEquals("在庫", results.get(0).zaikoChks());
        assertEquals(BigDecimal.valueOf(1234567891*1000L), results.get(0).knstsRiekiKkk1Tskm());
        assertEquals(BigDecimal.valueOf(1234567892*1000L), results.get(0).knstsRiekiKkk2Tskm());
        assertEquals(BigDecimal.valueOf(1234567893*1000L), results.get(0).knstsRiekiKkk3Tskm());
        assertEquals(BigDecimal.valueOf(1234567894*1000L), results.get(0).knstsRiekiKkk4Tskm());
        assertEquals(BigDecimal.valueOf(1234567895*1000L), results.get(0).knstsRiekiKkk5Tskm());
        assertEquals(BigDecimal.valueOf(1234567896*1000L), results.get(0).knstsRiekiKkk6Tskm());
        assertEquals(BigDecimal.valueOf(1234567897*1000L), results.get(0).knstsRiekiKkk7Tskm());
        assertEquals(BigDecimal.valueOf(1234567898*1000L), results.get(0).knstsRiekiKkk8Tskm());
        assertEquals(BigDecimal.valueOf(1234567899*1000L), results.get(0).knstsRiekiKkk9Tskm());
        assertEquals(BigDecimal.valueOf(1234567900*1000L), results.get(0).knstsRiekiKkk10Tskm());
        assertEquals(BigDecimal.valueOf(1234567901*1000L), results.get(0).knstsRiekiKkk11Tskm());
        assertEquals(BigDecimal.valueOf(1234567902*1000L), results.get(0).knstsRiekiKkk12Tskm());
        assertEquals("IMGBAK_006", results.get(0).trkPrgrmId());
        assertEquals("900001", results.get(0).trkSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(0).trkShainCode());
        assertEquals("IMGBAK_006", results.get(0).kshnPrgrmId());
        assertEquals("900001", results.get(0).kshnSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(0).kshnShainCode());
        assertEquals(1, results.get(0).vrsn());
        assertNotNull(results.get(0).rcrdTrkNchj());
        assertNotNull(results.get(0).rcrdKshnNchj());

        assertEquals("9999", results.get(1).nendo());
        assertEquals("9999904", results.get(1).makerCode());
        assertEquals(BigDecimal.valueOf(1004), results.get(1).makerKanriNo());
        assertEquals("4", results.get(1).ctgryCode());
        assertEquals("9910", results.get(1).groupCode());
        assertEquals("0", results.get(1).hnshBashoKubun());
        assertEquals("3", results.get(1).zaichokuKubun());
        assertEquals("4", results.get(1).mishuKubun());
        assertEquals("1118888", results.get(1).ssnknTncd());
        assertNull(results.get(1).ssnKanriTnmKanji());
        assertEquals("099996", results.get(1).systmUnyoKigyoCode());
        assertEquals("システムこグループこグループこグループこグループこグループこグループこグループこグループグ", results.get(1).systmUnyoKgymKanji());
        assertEquals("9800", results.get(1).areaCode());
        assertEquals("エリーアこグループこグループこグループこグループこグループグ", results.get(1).areaMeiKanji());
        assertEquals("0351", results.get(1).subAreaCode());
        assertEquals("エリーアこグループこグループこグループこグループこグループー", results.get(1).subAreaMeiKanji());
        assertEquals("グループこグループこグループこグループこグループこグループル", results.get(1).groupMeiKanji());
        assertNull(results.get(1).unitCode());
        assertNull( results.get(1).unitMeiKanji());
        assertEquals("その他未収", results.get(1).mishuMei());
        assertEquals("直送", results.get(1).zaikoChks());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk1Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk2Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk3Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk4Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk5Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk6Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk7Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk8Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk9Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk10Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk11Tskm());
        assertEquals(BigDecimal.valueOf(0), results.get(1).knstsRiekiKkk12Tskm());
        assertEquals("IMGBAK_006", results.get(1).trkPrgrmId());
        assertEquals("900001", results.get(1).trkSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(1).trkShainCode());
        assertEquals("IMGBAK_006", results.get(1).kshnPrgrmId());
        assertEquals("900001", results.get(1).kshnSystmUnyoKigyoCode());
        assertEquals("0NED01", results.get(1).kshnShainCode());
        assertEquals(1, results.get(1).vrsn());
        assertNotNull(results.get(1).rcrdTrkNchj());
        assertNotNull(results.get(1).rcrdKshnNchj());

        } catch (Exception e) {
            logger.error("更新_新規_正常系エラー: {}", e.getMessage(), e);
        } finally {
            // 新規と更新データ削除
          //  delRiekiMaker("9999",List.of("0999901","9999902","9999903","9999904"));
        }
    }

    /**
     * 試験対象：間接利益計画_メーカー別_インポート
     * 試験条件：複数件更新(全項目)、1件新規
     * data1:更新、異なるシステム運用企業コードと社員コード
     * data2:新規、メーカコードが「0011」で設定
     * 確認内容：
     * 　新規データがアップロードファイルの内容と一致する
     * 　既存データが更新プログラムＩＤと更新システム運用企業コードが既存と一致しないです。
     * 履歴のステータス: 完了
     */
    @Test
    @Order(10)
    void testExecuteImportTask_更新1_新規_正常系() {
        String uploadFilePath = "indirectprofitmakerimport/uploaddata/indirectprofitmaker_import_tst_data_20.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testImportRequest.setSystemAdminFlag("2");
        testImportRequest.setAreaCode("9801");
        testUserInfo.setSystemOperationCompanyCode("900031");
        testUserInfo.setShainCode("0NED02");

        try {
            DateUtil.setTestFixedDateTime("9999");

            // 実行と実行前後の履歴確認
            doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

            // 新規データ検証
            List<riekiKkkmaker> results = getDatabaseData("9999", List.of("9999903", "9999905"));
            assertThat(results).hasSize(2);

            assertEquals("9999", results.get(0).nendo());
            assertEquals("9999903", results.get(0).makerCode());
            assertEquals(BigDecimal.valueOf(1003), results.get(0).makerKanriNo());
            assertEquals("IMGBAK_006", results.get(0).trkPrgrmId());
            assertEquals("900001", results.get(0).trkSystmUnyoKigyoCode());
            assertEquals("0NED01", results.get(0).trkShainCode());
            assertEquals("IMGBAK_006", results.get(0).kshnPrgrmId());
            assertEquals("900031", results.get(0).kshnSystmUnyoKigyoCode());
            assertEquals("0NED02", results.get(0).kshnShainCode());

            assertEquals("9999", results.get(1).nendo());
            assertEquals("9999905", results.get(1).makerCode());
            assertEquals(BigDecimal.valueOf(11), results.get(1).makerKanriNo());

        } catch (Exception e) {
            logger.error("更新_新規_正常系エラー: {}", e.getMessage(), e);
        } finally {
            // 新規と更新データ削除
            delRiekiMaker("9999",List.of("0999901","9999902","9999903","9999904"));
        }
    }

    /**
     * 間接利益計画＿メーカー別からデータを削除する
     *
     */
    private void delRiekiMaker(String nendoStr,List<String> mackerCodes)
    {
        String sql = " delete from t_knsts_rieki_kkk_maker where nendo = ? and maker_code IN (%s)";
        String makerCodeStr = String.join(",", Collections.nCopies(mackerCodes.size(), "?"));
        String makerCodeSql = String.format(sql,makerCodeStr);
        Object[] params = Stream.concat(Stream.of(nendoStr), mackerCodes.stream()).toArray();

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            jdbcTemplate.update(makerCodeSql, params);

        } catch (SQLException e) {
            System.err.println("PostgreSQL 間接利益計画＿メーカー別削除エラー: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
    }

    /**
     * ImportJobStatusを追加と内容検証と情報を取得
     * 状態表データの検証用
     */
    private ImportJobStatus addAndCheckAndGetImportJobInfo() {
        try {
            // 事前にjobを手動追加する
            var importJobResponse = dataApplicationService.startImport(testImportRequest,
                    testUserInfo, mockLambdaContext);
            Long rrkBango = Long.parseLong(importJobResponse.getJobId());
            // ジョブが正常に作成されたことを確認
            assertNotNull(importJobResponse.getJobId());
            assertEquals("ACCEPTED", importJobResponse.getStatus());

            // 初期状態の確認
            ImportJobStatus initObj = getImportJobStatusFromDatabase(rrkBango);
            assertNotNull(initObj);
            assertEquals(rrkBango, initObj.getRrkBango());
            assertEquals(testUserInfo.getSystemOperationCompanyCode(), initObj.getSystmUnyoKigyoCode());
            assertEquals(testUserInfo.getShainCode(), initObj.getShainCode());
            assertEquals(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE, initObj.getFileShbts());
            assertNull(initObj.getArea());
            assertTrue(initObj.getFileMei().startsWith("indirectprofitmakerimport/uploaddata/"));
            assertNull(initObj.getErrorFileMei());
            assertNotNull(initObj.getUploadKshNchj());
            assertNull(initObj.getUploadKnrNchj());
            assertEquals(BusinessConstants.BATCH_STATUS_PROCESSING_CODE, initObj.getStts());
            assertEquals("IMGBAK_006", initObj.getTrkPrgrmId());
            assertEquals("SYSTEM", initObj.getTrkSystmUnyoKigyoCode());
            assertEquals("SYSTEM", initObj.getTrkShainCode());
            assertEquals("IMGBAK_006", initObj.getKshnPrgrmId());
            assertEquals("SYSTEM", initObj.getKshnSystmUnyoKigyoCode());
            assertEquals("SYSTEM", initObj.getKshnShainCode());
            assertEquals("1", initObj.getVrsn().toString());
            assertNotNull(initObj.getRcrdTrkNchj());
            assertNotNull(initObj.getRcrdKshnNchj());

            // 少し待機
            Thread.sleep(900);
            return initObj;
        } catch (Exception e) {
            System.err.println("エラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
        return null;
    }

    /**
     * 指定番号でステータスを比較する
     * 状態表データの検証用
     *
     * @param initObj 更新前履歴
     * @param status ステータス
     */
    private void checkImportJobInfo(ImportJobStatus initObj,String status) {
        ImportJobStatus finalObj = getImportJobStatusFromDatabase(initObj.getRrkBango());
        assertNotNull(finalObj);
        if (status.equals(BusinessConstants.BATCH_STATUS_COMPLETED_CODE)) {
            assertNull(finalObj.getErrorFileMei());
            assertNotNull(finalObj.getUploadKnrNchj());
        } else if (status.equals(BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE)) {
            assertNull(finalObj.getErrorFileMei());
            assertNull(finalObj.getUploadKnrNchj());
        } else {
            assertTrue(finalObj.getErrorFileMei() != null && finalObj.getErrorFileMei().startsWith("import-errors/error_間接利益計画_メーカー別"));
            assertNull(finalObj.getUploadKnrNchj());
        }

        assertEquals(status, finalObj.getStts());
        assertNotNull(finalObj.getRcrdKshnNchj());
        assertNotEquals(initObj.getRcrdKshnNchj(), finalObj.getRcrdKshnNchj());

        // 更新前と更新後の比較
        assertEquals(initObj.getRrkBango(), finalObj.getRrkBango());
        assertEquals(initObj.getSystmUnyoKigyoCode(), finalObj.getSystmUnyoKigyoCode());
        assertEquals(initObj.getShainCode(), finalObj.getShainCode());
        assertEquals(initObj.getFileShbts(), finalObj.getFileShbts());
        assertEquals(initObj.getArea(), finalObj.getArea());
        assertEquals(initObj.getFileMei(), finalObj.getFileMei());
        assertEquals(initObj.getUploadKshNchj(), finalObj.getUploadKshNchj());
        assertEquals(initObj.getTrkPrgrmId(), finalObj.getTrkPrgrmId());
        assertEquals(initObj.getTrkSystmUnyoKigyoCode(), finalObj.getTrkSystmUnyoKigyoCode());
        assertEquals(initObj.getTrkShainCode(), finalObj.getTrkShainCode());
        assertEquals(initObj.getKshnPrgrmId(), finalObj.getKshnPrgrmId());
        assertEquals(initObj.getKshnSystmUnyoKigyoCode(), finalObj.getKshnSystmUnyoKigyoCode());
        assertEquals(initObj.getKshnShainCode(), finalObj.getKshnShainCode());
        assertEquals(initObj.getVrsn().toString(), finalObj.getVrsn().toString());
        assertEquals(initObj.getRcrdTrkNchj(), finalObj.getRcrdTrkNchj());

        TestDataManager.deleteTableData("T_UPLOAD_RRK", List.of(Map.of("RRK_BANGO", initObj.getRrkBango())));
    }

    /**
     * データベースからImportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     */
    private ImportJobStatus getImportJobStatusFromDatabase(Long rrkBango) {
        try {
            // 真実のImportJobStatusServiceインスタンスを使用してデータベースから取得
            ImportJobStatusService importJobStatusService = new ImportJobStatusService();
            return importJobStatusService.getJobStatus(rrkBango);
        } catch (Exception e) {
            logger.error("ImportJobStatus取得エラー: rrkBango={}, error={}", rrkBango, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 実行と実行前後の履歴確認
     *
     * @param status 履歴のステータス
     */
    private void doExecute(String status) {
        // 実施前ダウンロード履歴jobを手動追加と内容検証
        var initObj = addAndCheckAndGetImportJobInfo();

        // テスト用WorkerPayloadを作成
        WorkerPayload payload=WorkerPayload.builder()
                .jobId(initObj.getRrkBango().toString())
                .operationType(BusinessConstants.OPERATION_UPLOAD_CODE)
                .request(testImportRequest)
                .userInfo(testUserInfo)
                .build();

        // executeImportTask を実行
        assertDoesNotThrow(() -> {
            dataApplicationService.executeImportTask(payload, mockLambdaContext);
        });

        // 実施後アップロード履歴jobの状態を検討
        checkImportJobInfo(initObj,status);
    }

    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            // S3Service の Mock 設定 - ファイルを本地保存するようにmock
            when(mockS3Service.uploadFileFromStream(any(InputStream.class), anyString(), anyString(), anyLong(), any()))
                    .thenAnswer(invocation -> {
                        InputStream inputStream = invocation.getArgument(0);
                        String s3Key = invocation.getArgument(1);

                        // S3キーからファイル名を抽出
                        String fileName = s3Key.substring(s3Key.lastIndexOf("/") + 1);

                        // テスト用のローカルディレクトリを作成
                        Path testDir = Files.createTempDirectory(prefix_folder);
                        if (!Files.exists(testDir)) {
                            Files.createDirectories(testDir);
                        }

                        // ローカルファイルパスを生成
                        Path localFilePath = testDir.resolve(fileName);

                        // InputStreamをローカルファイルに保存
                        try (OutputStream outputStream = Files.newOutputStream(localFilePath)) {
                            inputStream.transferTo(outputStream);
                        }

                        logger.info("ファイルをローカルに保存しました: {}", localFilePath.toAbsolutePath());

                        // S3アップロード成功の模擬レスポンスを返す
                        return Map.of(
                                "success", true,
                                "key", s3Key,
                                "localPath", localFilePath.toAbsolutePath().toString()
                        );
                    });

            // getInputStreamFromS3Url の動的Mock設定（setupMockS3で上書き可能）
            when(mockS3Service.getInputStreamFromS3Url(anyString()))
                    .thenAnswer(invocation -> {
                        String key = invocation.getArgument(0);
                        logger.debug("mockS3Service.getInputStreamFromS3Url 呼び出し: key={}", key);

                        // testdataディレクトリからファイルを自動読み込み（フォールバック）
                        ResponseInputStream<GetObjectResponse> autoLoadedResponse = tryAutoLoadTestData(key);
                        if (autoLoadedResponse != null) {
                            logger.debug("testdataから自動読み込み: key={}", key);
                            return autoLoadedResponse;
                        }

                        // デフォルトの空のResponseInputStreamを返す
                        logger.debug("デフォルトの空レスポンスを使用: key={}", key);
                        InputStream emptyStream = new ByteArrayInputStream(new byte[0]);
                        GetObjectResponse getObjectResponse = GetObjectResponse.builder().build();
                        return new ResponseInputStream<>(getObjectResponse, AbortableInputStream.create(emptyStream));
                    });

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(900000); // 15分（BackgroundTimeoutMonitorの緩衝時間10分より大きく設定）
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

            logger.debug("Mock オブジェクトの基本動作設定完了");
        } catch (Exception e) {
            logger.error("Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * S3Service の MockedConstruction を設定
     * 全てのS3Service構築をインターセプトし、mockS3Serviceの動作を委譲する
     */
    private void setupS3ServiceMockedConstruction() {
        logger.info("S3Service MockedConstruction 設定開始");

        mockedS3ServiceConstruction = mockConstruction(S3Service.class, (mock, context) -> {
            try {
                logger.info("S3Service インスタンス構築をインターセプト: mock={}, context={}",
                        mock.getClass().getSimpleName(), context);

                // uploadFileFromStream メソッドの委譲
                when(mock.uploadFileFromStream(any(InputStream.class), anyString(), anyString(), anyLong(), any()))
                        .thenAnswer(invocation -> {
                            logger.debug("S3Service.uploadFileFromStream 呼び出しを mockS3Service に委譲");
                            return mockS3Service.uploadFileFromStream(
                                    invocation.getArgument(0),
                                    invocation.getArgument(1),
                                    invocation.getArgument(2),
                                    invocation.getArgument(3),
                                    invocation.getArgument(4)
                            );
                        });

                // getInputStreamFromS3Url メソッドの委譲
                when(mock.getInputStreamFromS3Url(anyString()))
                        .thenAnswer(invocation -> {
                            String key = invocation.getArgument(0);
                            logger.debug("S3Service.getInputStreamFromS3Url 呼び出しを mockS3Service に委譲: key={}", key);
                            return mockS3Service.getInputStreamFromS3Url(key);
                        });

                // close メソッドの委譲
                doNothing().when(mock).close();

                logger.info("S3Service MockedConstruction 設定完了: mock={}", mock.getClass().getSimpleName());
            } catch (Exception e) {
                logger.error("S3Service MockedConstruction 設定エラー: {}", e.getMessage(), e);
                throw new RuntimeException("S3Service MockedConstruction 設定に失敗しました", e);
            }
        });

        logger.info("S3Service MockedConstruction を設定しました - 全てのS3Service構築がインターセプトされます");

        // MockedConstructionが正常に設定されたことを確認
        if (mockedS3ServiceConstruction != null) {
            logger.info("MockedConstruction<S3Service> が正常に初期化されました");
        } else {
            logger.error("MockedConstruction<S3Service> の初期化に失敗しました");
            throw new RuntimeException("MockedConstruction<S3Service> の初期化に失敗しました");
        }
    }

    /**
     * testdataディレクトリからファイルを自動読み込み
     * @param key S3キー
     * @return ResponseInputStream または null（ファイルが見つからない場合）
     */
    private ResponseInputStream<GetObjectResponse> tryAutoLoadTestData(String key) {
        try {
            String testDataPath = "testdata/" + key;
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(testDataPath);
            if (inputStream != null) {
                GetObjectResponse getObjectResponse = GetObjectResponse.builder().build();
                return new ResponseInputStream<>(getObjectResponse, AbortableInputStream.create(inputStream));
            }
        } catch (Exception e) {
            logger.debug("testdataからの自動読み込み失敗: key={}, error={}", key, e.getMessage());
        }
        return null;
    }

    /**
     * テスト用インポートリクエストを作成
     */
    private ImportRequest createTestImportRequest() {
        ImportRequest importRequest = new ImportRequest();
        importRequest.setS3Key("");
        importRequest.setDataType(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE);
        importRequest.setArea(null);
        importRequest.setGroupCode("0078");
        importRequest.setAreaCode("0000");
        importRequest.setUnitCode("11301");
        importRequest.setPositionCode("05");
        importRequest.setSystemAdminFlag("1");
        return importRequest;
    }

    /**
     * テスト用ユーザー情報を作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("0NED01");
        userInfo.setSystemOperationCompanyCode("900001");
        userInfo.setUnitCode("11300");
        AreaInfo area = new AreaInfo("0000", "テスト用_本社");
        AreaInfo area1 = new AreaInfo("0100", "テスト用_職能部門");
        AreaInfo area2 = new AreaInfo("0200", "テスト用_");
        userInfo.setAreaInfos(List.of(area, area1, area2));
        userInfo.setAreaCode("9800");
        userInfo.setAreaName("テスト用_本社");
        userInfo.setPositionCode("99");
        userInfo.setGroupCode("0099");
        userInfo.setPositionSpecialCheck("1");
        return userInfo;
    }

    /**
     * データ取得
     */
    private List<riekiKkkmaker> getDatabaseData(String nendoStr, List<String> mackerCodes )
    {
        List<riekiKkkmaker> results=null;
        String sql = """
                     select * from t_knsts_rieki_kkk_maker where nendo = ? and maker_code IN (%s)
                     order by nendo,maker_code
                     """;
        String tncdStr = String.join(",", Collections.nCopies(mackerCodes.size(), "?"));
        String tncdSql = String.format(sql,tncdStr);
        Object[] params = Stream.concat(Stream.of(nendoStr), mackerCodes.stream()).toArray();

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            results = jdbcTemplate.query(tncdSql, params, rs -> {
                String nendo = rs.getString("nendo");
                String makerCode = rs.getString("maker_code");
                BigDecimal makerKanriNo = rs.getBigDecimal("maker__kanri_no");
                String ctgryCode = rs.getString("ctgry_code");
                String groupCode = rs.getString("group_code");
                String hnshBashoKubun = rs.getString("hnsh_basho_kubun");
                String zaichokuKubun = rs.getString("zaichoku_kubun");
                String mishuKubun = rs.getString("mishu_kubun");
                String ssnknTncd = rs.getString("ssnkn_tncd");
                String ssnKanriTnmKanji = rs.getString("ssn_kanri_tnm_kanji");
                String systmUnyoKigyoCode = rs.getString("systm_unyo_kigyo_code");
                String systmUnyoKgymKanji = rs.getString("systm_unyo_kgym_kanji");
                String areaCode = rs.getString("area_code");
                String areaMeiKanji = rs.getString("area_mei_kanji");
                String subAreaCode = rs.getString("sub_area_code");
                String subAreaMeiKanji = rs.getString("sub_area_mei_kanji");
                String groupMeiKanji = rs.getString("group_mei_kanji");
                String unitCode = rs.getString("unit_code");
                String unitMeiKanji = rs.getString("unit_mei_kanji");
                String mishuMei = rs.getString("mishu_mei");
                String zaikoChks = rs.getString("zaiko_chks");
                BigDecimal knstsRiekiKkk1Tskm = rs.getBigDecimal("knsts_rieki_kkk_1_tskm");
                BigDecimal knstsRiekiKkk2Tskm = rs.getBigDecimal("knsts_rieki_kkk_2_tskm");
                BigDecimal knstsRiekiKkk3Tskm = rs.getBigDecimal("knsts_rieki_kkk_3_tskm");
                BigDecimal knstsRiekiKkk4Tskm = rs.getBigDecimal("knsts_rieki_kkk_4_tskm");
                BigDecimal knstsRiekiKkk5Tskm = rs.getBigDecimal("knsts_rieki_kkk_5_tskm");
                BigDecimal knstsRiekiKkk6Tskm = rs.getBigDecimal("knsts_rieki_kkk_6_tskm");
                BigDecimal knstsRiekiKkk7Tskm = rs.getBigDecimal("knsts_rieki_kkk_7_tskm");
                BigDecimal knstsRiekiKkk8Tskm = rs.getBigDecimal("knsts_rieki_kkk_8_tskm");
                BigDecimal knstsRiekiKkk9Tskm = rs.getBigDecimal("knsts_rieki_kkk_9_tskm");
                BigDecimal knstsRiekiKkk10Tskm = rs.getBigDecimal("knsts_rieki_kkk_10_tskm");
                BigDecimal knstsRiekiKkk11Tskm = rs.getBigDecimal("knsts_rieki_kkk_11_tskm");
                BigDecimal knstsRiekiKkk12Tskm = rs.getBigDecimal("knsts_rieki_kkk_12_tskm");
                String trkPrgrmId = rs.getString("trk_prgrm_id");
                String trkSystmUnyoKigyoCode = rs.getString("trk_systm_unyo_kigyo_code");
                String trkShainCode = rs.getString("trk_shain_code");
                String kshnPrgrmId = rs.getString("kshn_prgrm_id");
                String kshnSystmUnyoKigyoCode = rs.getString("kshn_systm_unyo_kigyo_code");
                String kshnShainCode = rs.getString("kshn_shain_code");
                int vrsn = rs.getInt("vrsn");
                String rcrdTrkNchj = rs.getString("rcrd_trk_nchj");
                String rcrdKshnNchj = rs.getString("rcrd_kshn_nchj");
                return new riekiKkkmaker(
                        nendo, makerCode, makerKanriNo, ctgryCode, groupCode, hnshBashoKubun,
                        zaichokuKubun, mishuKubun, ssnknTncd, ssnKanriTnmKanji,
                        systmUnyoKigyoCode, systmUnyoKgymKanji, areaCode, areaMeiKanji,
                        subAreaCode, subAreaMeiKanji, groupMeiKanji, unitCode, unitMeiKanji,
                        mishuMei, zaikoChks,
                        knstsRiekiKkk1Tskm, knstsRiekiKkk2Tskm, knstsRiekiKkk3Tskm,
                        knstsRiekiKkk4Tskm, knstsRiekiKkk5Tskm, knstsRiekiKkk6Tskm,
                        knstsRiekiKkk7Tskm, knstsRiekiKkk8Tskm, knstsRiekiKkk9Tskm,
                        knstsRiekiKkk10Tskm, knstsRiekiKkk11Tskm, knstsRiekiKkk12Tskm,
                        trkPrgrmId, trkSystmUnyoKigyoCode, trkShainCode, kshnPrgrmId,
                        kshnSystmUnyoKigyoCode, kshnShainCode, vrsn, rcrdTrkNchj, rcrdKshnNchj
                );
            });

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return results;
    }

    private record riekiKkkmaker(
            String nendo, String makerCode, BigDecimal makerKanriNo, String ctgryCode, String groupCode, String hnshBashoKubun, String zaichokuKubun, String mishuKubun, String ssnknTncd, String ssnKanriTnmKanji, String systmUnyoKigyoCode,
            String systmUnyoKgymKanji, String areaCode, String areaMeiKanji, String subAreaCode, String subAreaMeiKanji, String groupMeiKanji, String unitCode, String unitMeiKanji, String mishuMei, String zaikoChks,
            BigDecimal knstsRiekiKkk1Tskm, BigDecimal knstsRiekiKkk2Tskm, BigDecimal knstsRiekiKkk3Tskm, BigDecimal knstsRiekiKkk4Tskm, BigDecimal knstsRiekiKkk5Tskm,BigDecimal knstsRiekiKkk6Tskm,
            BigDecimal knstsRiekiKkk7Tskm, BigDecimal knstsRiekiKkk8Tskm, BigDecimal knstsRiekiKkk9Tskm, BigDecimal knstsRiekiKkk10Tskm, BigDecimal knstsRiekiKkk11Tskm, BigDecimal knstsRiekiKkk12Tskm,
            String trkPrgrmId, String trkSystmUnyoKigyoCode, String trkShainCode, String kshnPrgrmId, String kshnSystmUnyoKigyoCode, String kshnShainCode, int vrsn, String rcrdTrkNchj, String rcrdKshnNchj
    ) {}

    private List<String> getHeadOfFile(){
        return List.of(
                "エラーが発生したため、アップロード処理を終了しました。",
                "正常レコードについてもテーブル登録は行われておりませんので、再度全量アップロードを行ってください。",
                "エラー内容の出力は、エラー100件目を検出した行までの分となり、以降のチェック処理は行われません。",
                ""
        );
    }
}
