package com.ms.bp.interfaces.rest.controller;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.application.data.DataApplicationService;
import com.ms.bp.interfaces.dto.response.ExportJobResponse;
import com.ms.bp.interfaces.dto.response.ImportJobResponse;
import com.ms.bp.interfaces.dto.response.ExportHistoryResponse;
import com.ms.bp.interfaces.dto.response.ImportHistoryResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.shared.common.exception.ValidationException;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.util.RequestConversionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * データコントローラー
 * HTTPリクエスト/レスポンス処理のみを担当
 * ビジネスロジックはDataApplicationServiceに委譲
 */
public class DataController {
    private static final Logger logger = LoggerFactory.getLogger(DataController.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // アプリケーションサービス
    private final DataApplicationService dataApplicationService;

    public DataController() {
        this.dataApplicationService = new DataApplicationService();
    }

    /**
     * データエクスポート処理
     * HTTPリクエストを解析してアプリケーションサービスに委譲
     */
    public CommonResult<?> exportData(APIGatewayProxyRequestEvent request, UserInfo userInfo, Context context) {

        logger.debug("データエクスポートリクエスト: ユーザー={}", userInfo.getShainCode());
        String functionId = null;
        try {
            // リクエストボディからエクスポート設定を取得
            ExportRequest exportRequest = RequestConversionUtil.convertRequest(request, ExportRequest.class);

            // バリデーション
            exportRequest.validate();

            functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_DOWNLOAD_CODE, exportRequest.getDataType()).getFunctionId();

            // 開始ログ
            logger.info(formatMessage(GlobalMessageConstants.INF_001, functionId));

            // アプリケーションサービスに処理を委譲
            ExportJobResponse response = dataApplicationService.startExport(exportRequest, userInfo, context);

            return CommonResult.success(response);

        } catch (ValidationException e) {
            // バリデーションエラーの場合
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.VALIDATION_ERROR_MSG));
            throw e;
        } catch (Exception e) {
            // その他のエラーの場合
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.GENERAL_ERROR_MSG));
            logger.error("エクスポートリクエスト処理エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "エクスポートリクエストの処理中にエラーが発生しました");
        }
    }

    /**
     * エクスポート履歴取得
     * 分页処理は不要、デフォルトで最新20件のデータを取得する
     * ユーザーの権限に基づいてエクスポート履歴を取得し、
     * ファイル種別やエリア名を日本語表示に変換して返却する
     */
    public CommonResult<?> getExportHistory(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info(formatMessage(GlobalMessageConstants.INF_001, "共通処理.ダウンロード履歴取得"));

            // アプリケーションサービスに処理を委譲（固定で最新20件を取得）
            List<ExportHistoryResponse> response = dataApplicationService.getExportHistory(userInfo);

            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.ダウンロード履歴取得", BusinessConstants.SUCCESS_MSG));

            return CommonResult.success(response);

        } catch (Exception e) {
            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.ダウンロード履歴取得", BusinessConstants.GENERAL_ERROR_MSG));
            logger.error("ダウンロード履歴取得エラー: ユーザー={}, エラー内容={}",
                        userInfo.getShainCode(), e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ダウンロード履歴取得中にエラーが発生しました");
        }
    }

    /**
     * データインポート処理
     * HTTPリクエストを解析してアプリケーションサービスに委譲
     */
    public CommonResult<?> importData(APIGatewayProxyRequestEvent request, UserInfo userInfo, Context context) {

        String functionId = null;
        try {
            // リクエストボディからインポート設定を取得
            ImportRequest importRequest = RequestConversionUtil.convertRequest(request, ImportRequest.class);

            // バリデーション
            importRequest.validate();

            functionId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE, importRequest.getDataType()).getFunctionId();

            // 開始ログ
            logger.info(formatMessage(GlobalMessageConstants.INF_001, functionId));

            // アプリケーションサービスに処理を委譲
            ImportJobResponse response = dataApplicationService.startImport(importRequest, userInfo, context);

            return CommonResult.success(response);

        } catch (ValidationException e) {
            // バリデーションエラーの場合
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.VALIDATION_ERROR_MSG));
            throw e;
        } catch (ServiceException e) {
            logger.info(formatMessage(GlobalMessageConstants.INF_002, functionId, BusinessConstants.GENERAL_ERROR_MSG));
            throw e;
        } catch (Exception e) {
            logger.error("インポートリクエスト処理エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "インポートリクエストの処理中にエラーが発生しました");
        }
    }

    /**
     * インポート履歴取得
     * 分页処理は不要、最新20件のインポート履歴を取得する
     */
    public CommonResult<?> getImportHistory(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info(formatMessage(GlobalMessageConstants.INF_001, "共通処理.アップロード履歴取得"));

            // アプリケーションサービスに処理を委譲（分页パラメータなし）
            List<ImportHistoryResponse> response = dataApplicationService.getImportHistory(userInfo);
            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.アップロード履歴取得", BusinessConstants.SUCCESS_MSG));
            return CommonResult.success(response);

        } catch (Exception e) {
            logger.info(formatMessage(GlobalMessageConstants.INF_002, "共通処理.アップロード履歴取得", BusinessConstants.GENERAL_ERROR_MSG));
            logger.error("アップロード履歴取得エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "アップロード履歴取得中にエラーが発生しました");
        }
    }

}