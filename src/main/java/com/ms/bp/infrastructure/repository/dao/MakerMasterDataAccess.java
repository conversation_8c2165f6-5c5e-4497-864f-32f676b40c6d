package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * グループマスタデータアクセス実装
 * M_GROUPMST（グループマスタ）への具体的なデータアクセスを実装
 */
public class MakerMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(MakerMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_MAKER_SQL = """
        SELECT 1
        FROM M_MAKERMST
        WHERE MAKER_CODE = ?
        AND SHIYO_KNSH_KUBUN = '0'
        """;

    private final JdbcTemplate jdbcTemplate;

    public MakerMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * メーカーコードでメーカーマスタの存在チェック
     *
     * @param makerCode メーカーコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByMakerCode(String makerCode) throws SQLException {
        logger.debug("メーカーマスタ存在チェック開始: メーカーコード={}", makerCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_MAKER_SQL,
            new Object[]{makerCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("メーカーマスタ存在チェック完了: メーカーコード={}, 存在={}", makerCode, exists);

        return exists;
    }
}
