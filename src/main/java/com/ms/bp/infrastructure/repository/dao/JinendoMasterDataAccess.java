package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.file.indirectprofitmaker.IndirectProfitMakerImportService;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.model.JinendoMasterInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.stream.Collectors;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Stream;

/**
 * 次年度計画マスタデータアクセス実装
 * T_JINENDO_KKK（次年度計画マスタ）への具体的なデータアクセスを実装
 */
public class JinendoMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(JinendoMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_SSNKN_TNCD_SQL = """
        SELECT 1
        FROM T_JINENDO_KKK
        WHERE NENDO = ?
        AND   SSNKN_TNCD = ?
        AND   TKY_AREA_CODE = ?
        AND   TKY_GROUP_CODE = ?
        AND   TKY_UNIT_CODE = ?
        LIMIT 1
        """;

    /*
    // SQL定数
    private static final String EXISTS_BY_SSNKN_TNCD_SQL2 = """
        SELECT 1
        FROM T_JINENDO_KKK
        WHERE NENDO = ?
        AND   SSNKN_TNCD = ?
        AND   TKY_AREA_CODE = ?
        AND   TKY_GROUP_CODE = ?
        LIMIT 1
        """;
     */

    // SQL定数
    private static final String EXISTS_BY_IKNSK_SSNKN_TNCD_SQL = """
        SELECT 1
        FROM T_JINENDO_KKK
        WHERE NENDO = ?
        AND   SSNKN_TNCD = ?
        AND   IKNSK_AREA_CODE = ?
        AND   IKNSK_GROUP_CODE = ?
        AND   IKNSK_UNIT_CODE = ?
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public JinendoMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 採算管理単位コードでグループマスタの存在チェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @param unitCode ユニットコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsBySaisanCode(String nendo,
                                      String saisanCode,
                                      String areaCode,
                                      String groupCode,
                                      String unitCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始（ユニットコードあり）: 採算管理単位コード={}", saisanCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_SSNKN_TNCD_SQL,
            new Object[]{nendo, saisanCode, areaCode, groupCode, unitCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了（ユニットコードあり）: 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }
    /**
     * 採算管理単位コードでグループマスタの存在チェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    /*
    public boolean existsBySaisanCode(String nendo,
                                      String saisanCode,
                                      String areaCode,
                                      String groupCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始: 採算管理単位コード={}", saisanCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_SSNKN_TNCD_SQL2,
                new Object[]{nendo, saisanCode, areaCode, groupCode},
                rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了: 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }
     */

    /**
     * 採算管理単位コードでグループマスタの存在チェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @param unitCode ユニットコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByIknskSaisanCode(String nendo,
                                           String saisanCode,
                                           String areaCode,
                                           String groupCode,
                                           String unitCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始(移管): 採算管理単位コード={}", saisanCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_IKNSK_SSNKN_TNCD_SQL,
                new Object[]{nendo, saisanCode, areaCode, groupCode, unitCode},
                rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了(移管): 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }

    /**
     * 採算管理単位コード、グループコードがエリア＿見通し・計画_採算管理単位C別、次年度計画マスタに存在するかのチェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsBysnknTncdAndGroupCode(String nendo, String saisanCode, String groupCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始(移管): 採算管理単位コード={}", saisanCode);
        String sql = """
                  select
                    j.nendo ,j.ssnkn_tncd
                  from T_JINENDO_KKK as j
                  join T_AREA_MTSH_KKK_SSNKN_TN_C as c on j.nendo =c.nendo
                  and j.ssnkn_tncd=c.ssnkn_tncd
                  and (j.tky_group_code=c.group_code or j.group_code =c.group_code )
                  where j.nendo = ?
                  and j.ssnkn_tncd = ?
                  and j.tky_group_code = ?
                """;

        List<Integer> results = jdbcTemplate.query(
                sql,
                new Object[]{nendo, saisanCode, groupCode},
                rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了(移管): 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }

    /**
     * 採算管理単位コード、グループコードがで次年度計画マスタの情報を取得する
     *
     * @param nendoPara 年度
     * @param pairsToSearch <グループコード,採算管理コード>
     * @return 次年度計画マスタの情報
     * @throws SQLException データベースアクセスエラー
     */
    public List<JinendoMasterInfo> findBySnknTncdAndGroupCode(String nendoPara, List<IndirectProfitMakerImportService.GroupSaisanPair> pairsToSearch) throws SQLException {
        logger.debug("次年度計画マスタの情報を取得開始: ＜採算管理単位コード、グループコード＞の件数={}", pairsToSearch.size());

        String valuesPlaceholders = pairsToSearch.stream()
                .map(p -> "(?, ?)")
                .collect(Collectors.joining(", "));

        String sql = "SELECT distinct t.nendo,t.group_code,t.ssnkn_tncd,t.ssn_kanri_tnm_kanji " +
                "FROM T_JINENDO_KKK t " +
                "JOIN (VALUES " + valuesPlaceholders + ") AS csv_data(saisan_code_in_query, group_code_in_query) " +
                "ON  t.ssnkn_tncd = csv_data.saisan_code_in_query " +
                "AND t.group_code = csv_data.group_code_in_query " +
                "WHERE t.nendo = ?";

        Object[] params = pairsToSearch.stream()
                .flatMap(pair -> Stream.of( pair.ssnknTncd(),pair.groupCode()))
                .toArray();

        params = Stream.concat(Arrays.stream(params), Stream.of(nendoPara)).toArray();

        List<JinendoMasterInfo> results = jdbcTemplate.query(
                sql,
                params,
                rs -> {
                    String nendo = rs.getString("nendo");
                    String groupCode = rs.getString("group_code");
                    String ssnknTncd = rs.getString("ssnkn_tncd");
                    String ssnKanriTnmKanji = rs.getString("ssn_kanri_tnm_kanji");
                    return new JinendoMasterInfo(nendo, groupCode, ssnknTncd, ssnKanriTnmKanji);
                });

        logger.debug("次年度計画マスタの情報を取得完了: 取得した＜採算管理単位コード、グループコード＞の件数={}", results.size());

        return results;
    }
}
