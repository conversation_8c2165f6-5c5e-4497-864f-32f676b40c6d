package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * グループマスタデータアクセス実装
 * M_GROUPMST（グループマスタ）への具体的なデータアクセスを実装
 */
public class GroupMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(GroupMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_GROUP_CODE_SQL = """
        SELECT 1
        FROM M_GROUPMST
        WHERE GROUP_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
        AND SHIYO_KNSH_KUBUN = '0'
        LIMIT 1
        """;

    // SQL定数
    private static final String FIND_BY_GROUP_CODE_SQL = """
        SELECT
           group_code,
           area_code
        FROM M_GROUPMST
        WHERE GROUP_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
        AND SHIYO_KNSH_KUBUN = '0'
        ORDER BY KSHB DESC
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public GroupMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * グループコードでグループマスタの存在チェック
     *
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByGroupCode(String groupCode) throws SQLException {
        logger.debug("グループマスタ存在チェック開始: グループコード={}", groupCode);

        List<Integer> results = jdbcTemplate.query(
            EXISTS_BY_GROUP_CODE_SQL,
            new Object[]{groupCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("グループマスタ存在チェック完了: グループコード={}, 存在={}", groupCode, exists);

        return exists;
    }

    /**
     * グループコードでグループマスタの取得
     * 指定されたグループコードがグループマスタ情報を取得する
     *
     * @param groupCodePara グループコード
     * @return 指定されたグループコードに対するグループマスタ情報
     */
    public List<GroupMasterInfo> findByGroupCode(String groupCodePara) throws SQLException {
        logger.debug("グループマスタ情報検索開始: グループコード={}", groupCodePara);

        List<GroupMasterInfo> groupInfos = jdbcTemplate.query(FIND_BY_GROUP_CODE_SQL, new Object[]{groupCodePara}, rs -> {
            GroupMasterInfo info = new GroupMasterInfo();
            info.setGroupCode(rs.getString("group_code"));
            info.setAreaCode(rs.getString("area_code"));
            return info;
        });

        logger.debug("グループマスタ情報取得結果: 件数={}", groupInfos.size());
        logger.debug("取得したグループマスタ情報: {}", groupInfos);
        if (groupInfos.isEmpty()) {
            logger.warn("M_GROUPMSTテーブルからグループマスタ情報が取得できませんでした: グループコード={}", groupCodePara);
        }

        return groupInfos;
    }

    /**
     * グループコードでグループマスタに関する情報の取得
     * 指定されたグループコードがグループマスタ情報などを取得する
     *
     * @param groupCodes グループコードリスト
     * @return 指定されたグループコードに対するグループマスタ情報など
     */
    public List<GroupMasterInfo> findGroupAndAreaInfoByGroupCode(List<String> groupCodes) throws SQLException {
        logger.debug("グループ情報とエリア情報とシステム運用企業情報の検索開始: グループコードの件数={}", groupCodes.size());
        String valuesPlaceholders = groupCodes.stream()
                .map(code -> "(?)")
                .collect(Collectors.joining(", "));

        String sql1 = """
                       select
                          DISTINCT ON (gm.group_code)
                          gm.group_code,
                          gm.group_mei_kanji,
                          gm.systm_unyo_kigyo_code,
                          systemkigyo_mst.systm_unyo_kgym_kanji,
                          gm.area_code,
                          soshiki_mst.area_mei_kanji,
                          gm.sub_area_code,
                          soshiki_mst.sub_area_mei_kanji
                       FROM M_GROUPMST as gm
                       LEFT JOIN LATERAL (
                               SELECT systm_unyo_kigyo_code ,systm_unyo_kgym_kanji
                               FROM m_systemunyokigyomst
                               WHERE systm_unyo_kigyo_code = gm.systm_unyo_kigyo_code
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
                               AND shiyo_knsh_kubun = '0'
                               ORDER BY kshb DESC
                               LIMIT 1
                           ) AS systemkigyo_mst ON true
                       LEFT JOIN LATERAL (
                               SELECT area_code ,area_mei_kanji,sub_area_code,sub_area_mei_kanji
                               FROM m_soshikiareamst
                               WHERE area_code =  gm.area_code
                               and sub_area_code =  gm.sub_area_code
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= kshb
                               AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= shryb
                               AND shiyo_knsh_kubun = '0'
                               ORDER BY kshb DESC
                               LIMIT 1
                           ) AS soshiki_mst ON true
                       JOIN (
                         VALUES
                """;

        String sql2 = """
                       ) AS csv_data(group_code_in_query) on  csv_data.group_code_in_query=gm.group_code
                       where TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= gm.KSHB
                       AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= gm.SHRYB
                       AND gm.SHIYO_KNSH_KUBUN = '0'
                       ORDER BY gm.group_code, gm.kshb DESC
                """;
        String sql = sql1 + valuesPlaceholders + sql2;

        List<GroupMasterInfo> masterInfos = jdbcTemplate.query(sql, groupCodes.toArray(), rs -> {
            GroupMasterInfo info = new GroupMasterInfo();
            info.setGroupCode(rs.getString("group_code"));
            info.setGroupMeiKanji(rs.getString("group_mei_kanji"));
            info.setAreaCode(rs.getString("area_code"));
            info.setAreaMeiKanji(rs.getString("area_mei_kanji"));
            info.setSubAreaCode(rs.getString("sub_area_code"));
            info.setSubAreaMeiKanji(rs.getString("sub_area_mei_kanji"));
            info.setSystmUnyoKigyoCode(rs.getString("systm_unyo_kigyo_code"));
            info.setSystmUnyoKgymKanji(rs.getString("systm_unyo_kgym_kanji"));
            return info;
        });

        logger.debug("グループ情報とエリア情報とシステム運用企業情報の取得結果: 件数={}", masterInfos.size());

        if (masterInfos.isEmpty()) {
            logger.warn("グループ情報とエリア情報とシステム運用企業情報の取得できませんでした");
        }

        return masterInfos;
    }
}
