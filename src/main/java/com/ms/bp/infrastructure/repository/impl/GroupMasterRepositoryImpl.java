package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.infrastructure.repository.dao.GroupMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * ユーザーマスタリポジトリ実装
 * M_GROUPMST（グループマスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class GroupMasterRepositoryImpl implements GroupMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(GroupMasterRepositoryImpl.class);

    private final GroupMasterDataAccess dataAccess;

    public GroupMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new GroupMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsByGroupCode(String groupCode) {
        try {
            boolean exists = dataAccess.existsByGroupCode(groupCode);
            logger.debug("グループマスタ存在チェックを実行しました: グループコード={}, 存在={}", groupCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("グループマスタ存在チェック中にエラーが発生しました: グループコード={}", groupCode, e);
            throw new RuntimeException("グループマスタ存在チェックに失敗しました", e);
        }
    }

    /**
     * グループコードでグループマスタの取得
     * 指定されたグループコードがグループマスタ情報を取得する
     *
     * @param groupCode グループコード
     * @return 指定されたグループコードに対するグループマスタ情報
     */
    @Override
    public List<GroupMasterInfo> findByGroupCode(String groupCode) {
        try {
            List<GroupMasterInfo> groupInfos = dataAccess.findByGroupCode(groupCode);
            logger.debug("グループマスタ情報を取得しました: グループコード={}, 件数={}", groupCode, groupInfos.size());
            return groupInfos;
        } catch (SQLException e) {
            logger.error("グループマスタ情報取得中にエラーが発生しました: グループコード={}", groupCode, e);
            throw new RuntimeException("グループマスタ情報取得に失敗しました", e);
        }
    }

    /**
     * グループコードでグループマスタに関する情報の取得
     * 指定されたグループコードがグループマスタ情報などを取得する
     *
     * @param groupCodes グループコードリスト
     * @return 指定されたグループコードに対するグループマスタ情報など
     */
    @Override
    public List<GroupMasterInfo> findGroupAndAreaInfoByGroupCode(List<String> groupCodes) {
        try {
            List<GroupMasterInfo> groupInfos = dataAccess.findGroupAndAreaInfoByGroupCode(groupCodes);
            logger.debug("グループ情報とエリア情報とシステム運用企業情報を取得しました: グループコードの件数={}", groupInfos.size());
            return groupInfos;
        } catch (SQLException e) {
            logger.error("グループ情報とエリア情報とシステム運用企業情報取得中にエラーが発生しました: グループコードの件数={}", groupCodes.size(), e);
            throw new RuntimeException("グループ情報とエリア情報とシステム運用企業情報取得に失敗しました", e);
        }
    }
}
