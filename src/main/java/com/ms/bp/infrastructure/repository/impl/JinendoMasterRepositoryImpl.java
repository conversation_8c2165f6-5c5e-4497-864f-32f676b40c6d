package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.file.indirectprofitmaker.IndirectProfitMakerImportService;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.model.JinendoMasterInfo;
import com.ms.bp.domain.master.repository.JinendoMasterRepository;
import com.ms.bp.infrastructure.repository.dao.JinendoMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 次年度計画マスタリポジトリ実装
 * T_JINENDO_KKK（次年度計画マスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class JinendoMasterRepositoryImpl implements JinendoMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(JinendoMasterRepositoryImpl.class);

    private final JinendoMasterDataAccess dataAccess;

    public JinendoMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new JinendoMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode, String unitCode) {
        try {
            boolean exists = dataAccess.existsBySaisanCode(nendo, saisanCode, areaCode, groupCode, unitCode);
            if(!exists){
                exists = dataAccess.existsByIknskSaisanCode(nendo, saisanCode, areaCode, groupCode, unitCode);
            }
            logger.debug("次年度計画マスタ存在チェックを実行しました: 採算管理単位コード={}, 存在={}", saisanCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("次年度計画マスタ存在チェック中にエラーが発生しました: 採算管理単位コード={}", saisanCode, e);
            throw new RuntimeException("次年度計画マスタ存在チェックに失敗しました", e);
        }
    }

    /*
    @Override
    public boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode) {
        try {
            boolean exists = dataAccess.existsBySaisanCode(nendo, saisanCode, areaCode, groupCode);
            logger.debug("次年度計画マスタ存在チェックを実行しました: 採算管理単位コード={}, 存在={}", saisanCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("次年度計画マスタ存在チェック中にエラーが発生しました: 採算管理単位コード={}", saisanCode, e);
            throw new RuntimeException("次年度計画マスタ存在チェックに失敗しました", e);
        }
    }
     */

    /**
     * 採算管理単位コード、グループコードがエリア＿見通し・計画_採算管理単位C別、次年度計画マスタに存在するかのチェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    @Override
    public boolean existsBySnknTncdAndGroupCode(String nendo, String saisanCode, String groupCode) {
        try {
            boolean exists = dataAccess.existsBysnknTncdAndGroupCode(nendo, saisanCode, groupCode);
            logger.debug("見通し・計画_採算管理単位C別、次年度計画マスタに存在チェックを実行しました: 年度={}, 採算管理単位コード={},グループコード={}, 存在={}", nendo,saisanCode, groupCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("見通し・計画_採算管理単位C別、次年度計画マスタに存在チェック中にエラーが発生しました: 年度={}, 採算管理単位コード={},グループコード={}",nendo, saisanCode, groupCode, e);
            throw new RuntimeException("見通し・計画_採算管理単位C別、次年度計画マスタに存在チェックに失敗しました", e);
        }
    }

    /**
     * 採算管理単位コード、グループコードがで次年度計画マスタの情報を取得する
     *
     * @param nendo 年度
     * @param pairsToSearch <グループコード,採算管理コード>
     * @return 次年度計画マスタの情報
     * @throws SQLException データベースアクセスエラー
     */
    @Override
    public  List<JinendoMasterInfo> findBySnknTncdAndGroupCode(String nendo, List<IndirectProfitMakerImportService.GroupSaisanPair> pairsToSearch) {
        try {
            List<JinendoMasterInfo> jinendoMasterInfo = dataAccess.findBySnknTncdAndGroupCode(nendo,pairsToSearch);
            logger.debug("次年度計画マスタの情報を取得しました: グループコード={}, 件数={}", "", jinendoMasterInfo.size());
            return jinendoMasterInfo;
        } catch (SQLException e) {
            logger.error("次年度計画マスタの情報取得中にエラーが発生しました: グループコード={}", "", e);
            throw new RuntimeException("次年度計画マスタの情報取得に失敗しました", e);
        }
    }
}
