package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.domain.master.repository.MakerMasterRepository;
import com.ms.bp.infrastructure.repository.dao.GroupMasterDataAccess;
import com.ms.bp.infrastructure.repository.dao.MakerMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * メーカーマスタリポジトリ実装
 * M_MAKERMST（メーカーマスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class MakerMasterRepositoryImpl implements MakerMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(MakerMasterRepositoryImpl.class);

    private final MakerMasterDataAccess dataAccess;

    public MakerMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new MakerMasterDataAccess(jdbcTemplate);
    }

    /**
     * メーカーコードでメーカーマスタの存在チェック
     *
     * @param makerCode メーカーコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    @Override
    public boolean existsByMakerCode(String makerCode) {
        try {
            boolean exists = dataAccess.existsByMakerCode(makerCode);
            logger.debug("メーカーマスタ存在チェックを実行しました: グループコード={}, 存在={}", makerCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("メーカーマスタ存在チェック中にエラーが発生しました: グループコード={}", makerCode, e);
            throw new RuntimeException("メーカーマスタ存在チェックに失敗しました", e);
        }
    }
}
