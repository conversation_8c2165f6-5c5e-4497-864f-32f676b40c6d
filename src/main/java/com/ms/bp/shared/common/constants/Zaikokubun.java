package com.ms.bp.shared.common.constants;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 在庫・直送区分を定義するEnum
 */
public enum Zaikokubun {
    ZAIKO(1, "在庫"),
    CHOKUSO(3, "直送");

    private final int code;
    private final String text;

    Zaikokubun(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    private static final Map<Integer, Zaikokubun> LOOKUP_MAP = Stream.of(values())
            .collect(Collectors.toMap(Zaikokubun::getCode, e -> e));

    /**
     * 数字コードから対応するEnum定数を取得します。
     * @param code 数字コード
     * @return 対応するZaichokuKubun。見つからない場合はnullを返します。
     */
    public static Zaikokubun getByCode(int code) {
        return LOOKUP_MAP.get(code);
    }
}