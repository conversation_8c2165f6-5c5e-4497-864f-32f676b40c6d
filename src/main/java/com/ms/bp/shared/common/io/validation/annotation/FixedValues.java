package com.ms.bp.shared.common.io.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 固定値集合チェックアノテーション
 * 入力値が指定された固定値集合に含まれているかを検証
 * GlobalMessageConstants.ERR_031と統合されたバリデーション機能を提供
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FixedValues {

    /**
     * 許可される値の配列
     * 例：{"1", "2", "3"} または {"A", "B", "C"}
     */
    String[] values();

    /**
     * カスタム日本語フィールド名
     * 指定された場合、エラーメッセージでJavaフィールド名の代わりに使用される
     * 例：fieldName = "ステータス"
     */
    String fieldName() default "";

    /**
     * エラーコード（デフォルト：ERR_031）
     * GlobalMessageConstantsで定義されたエラーコードを指定
     */
    String errorCode() default "ERR_031";

    /**
     * カスタムエラーメッセージ
     * 空の場合はerrorCodeに対応するGlobalMessageConstantsのメッセージテンプレートを使用
     * 指定された場合は{field}、{values}占位符をサポート
     */
    String message() default "";
}
