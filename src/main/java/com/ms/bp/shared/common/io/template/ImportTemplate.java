package com.ms.bp.shared.common.io.template;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.io.converter.DTOConverter;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.mapper.DTOMapper;
import com.ms.bp.shared.common.io.options.DeleteCondition;
import com.ms.bp.shared.common.io.options.DeleteConditionProvider;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import com.ms.bp.shared.common.io.strategy.FileParsingStrategy;
import com.ms.bp.shared.common.io.factory.FileParsingStrategyFactory;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * インポートテンプレート (JDBC版)
 * （テンプレートメソッドパターン：インポートプロセスを定義し、特定のステップを拡張可能にする）
 * パフォーマンス最適化：DatabaseMappableを実装するDTOのみを受け入れ
 */
public abstract class ImportTemplate<T extends DatabaseMappable> implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(ImportTemplate.class);
    private static final int MAX_ERROR_COUNT = 100;
    private final S3Service s3Service;

    // インポートセッション用キャッシュ
    private ImportSessionCache sessionCache;

    /**
     * デフォルトコンストラクタ - 通常の実行時に使用
     */
    public ImportTemplate() {
        this.s3Service = new S3Service();
    }

    /**
     * テスト用コンストラクタ - S3Serviceを外部から注入
     */
    public ImportTemplate(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    /**
     * リソースをクリーンアップ
     */
    @Override
    public void close() {
        try {
            s3Service.close();
            // セッションキャッシュをクリア
            if (sessionCache != null) {
                sessionCache.clear();
            }
        } catch (Exception e) {
            logger.error("リソースのクローズ中にエラーが発生しました", e);
        }
    }

    /**
     * テンプレートメソッド：インポートプロセスを定義（DTOベース）
     */
    public final ImportResult executeImport(
            InputStream inputStream,
            Class<T> dtoClass,
            ImportOptions options,
            Context lambdaContext,
            DataValidator validator) throws SQLException, IOException {

        ImportResult result = new ImportResult();
        long startTime = System.currentTimeMillis();

        // インポートセッション用キャッシュを初期化
        this.sessionCache = new ImportSessionCache();

        try {
            // ステップ1：パラメータを検証
            validateParameters(options, dtoClass);

            // ステップ3：入力データを解析
            List<Map<String, Object>> rawRecords = parseInputData(inputStream, options);
            logger.info("入力ファイルから{}件のレコードを解析しました", rawRecords.size());

            // ステップ4：データを検証（必要な場合）
            if (!options.isSkipValidation()) {
                validateRecords(rawRecords, options, result, validator);

                // 検証に失敗し、続行しない場合は戻る
                if (result.getFailedCount() > 0 && !options.isContinueOnError()) {
                    return result;
                }
            }

            // ステップ5：バッチ単位で処理し、各バッチを個別のトランザクションで実行
            int batchSize = options.getBatchSize();
            List<Map<String, Object>> batch = new ArrayList<>(batchSize);

            for (int i = 0; i < rawRecords.size(); i++) {
                Map<String, Object> rawData = rawRecords.get(i);

                // Lambdaがタイムアウトに近づいているか確認
                if (shouldCheckTimeout(i) &&
                        AuroraConnectionManager.isNearTimeout(lambdaContext)) {
                    logger.error("Lambda実行がタイムアウトに近づいているため、インポートを一時停止しました");
                    break;
                }

                batch.add(rawData);

                // バッチがいっぱいになったら処理（個別のトランザクションで）
                if (batch.size() >= batchSize) {
                    processBatchWithTransaction(batch, dtoClass, options, result);
                    batch.clear();
                }
            }

            // 最後の不完全なバッチを処理（これも個別のトランザクションで）
            if (!batch.isEmpty()) {
                processBatchWithTransaction(batch, dtoClass, options, result);
            }

            // ステップ6：統計情報を記録
            result.addStatistic("totalProcessed", result.getInsertedCount() + result.getUpdatedCount());

            logger.info("インポート完了: {}件削除, {}件挿入, {}件更新, {}件失敗",
                    result.getDeletedCount(), result.getInsertedCount(),
                    result.getUpdatedCount(), result.getFailedCount());
        } catch (Exception e) {
            // ERR_010の場合、エラーファイル出力
            if (e instanceof ServiceException serviceException
                    && GlobalMessageConstants.ERR_010.getCode().equals(serviceException.getCode())) {
                writeErrorFile(options,result,serviceException.getMessage());
                result.addStatistic("headCheck",true);
            }else{
                logger.error("インポート失敗", e);
                throw e;
            }
        } finally {
            // 入力ストリームを閉じる
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                logger.error("入力ストリームを閉じる際にエラーが発生しました", e);
            }

            // 実行統計情報を追加
            long endTime = System.currentTimeMillis();
            result.addStatistic("executionTimeMs", endTime - startTime);
        }

        return result;
    }

    /**
     * 個別のトランザクションでバッチ処理を実行 (JDBC版・DTOベース)
     */
    protected void processBatchWithTransaction(
            List<Map<String, Object>> batch,
            Class<T> dtoClass,
            ImportOptions options,
            ImportResult result) throws SQLException {

        processBatch(batch, dtoClass, options, result);
    }

    /**
     * パラメータを検証（DTOベース）
     */
    protected void validateParameters(ImportOptions options, Class<T> dtoClass) throws IllegalArgumentException {

        if (dtoClass == null) {
            throw new IllegalArgumentException("DTOクラスがnullです");
        }

        // DatabaseMappableインターフェースの実装チェック
        if (!DatabaseMappable.class.isAssignableFrom(dtoClass)) {
            throw new IllegalArgumentException("DTOクラスはDatabaseMappableインターフェースを実装している必要があります: " + dtoClass.getSimpleName());
        }

        if (options.getTargetTable() == null || options.getTargetTable().isEmpty()) {
            throw new IllegalArgumentException("ターゲットテーブルがオプションで指定されていません");
        }

        if (options.isUpsertMode() && (options.getKeyColumns() == null || options.getKeyColumns().isEmpty())) {
            throw new IllegalArgumentException("UPSERTモードではキー列の指定が必要です");
        }
    }

    /**
     * 入力データを解析
     */
    protected List<Map<String, Object>> parseInputData(InputStream inputStream, ImportOptions options) throws IOException {
        FileParsingStrategy strategy = FileParsingStrategyFactory.createStrategy(options.getFormat());
        return strategy.parseFile(inputStream, options);
    }

    /**
     * レコードを検証（エラーをTXTファイルに記録）
     */
    protected void validateRecords(List<Map<String, Object>> rawRecords, ImportOptions options, ImportResult result, DataValidator validator) throws IOException {
        ByteArrayOutputStream txtOutputStream = new ByteArrayOutputStream();

        try (PrintWriter txtWriter = new PrintWriter(new OutputStreamWriter(txtOutputStream, StandardCharsets.UTF_8))) {
            int errorCount = 0;
            int currentRowErrorCount=0;
            for (int i = 0; i < rawRecords.size(); i++) {
                Map<String, Object> rawData = rawRecords.get(i);
                int rowNumber = i + 1;

                try {
                    // バリデーターを使用してデータを検証
                    List<ValidationError> validationErrors = validator.validate(rawData, options);

                    // エラーがある場合、すべてのエラーを記録
                    if (!validationErrors.isEmpty()) {
                        // エラー数を実際のエラー発生回数で計算
                        errorCount += validationErrors.size();
                        currentRowErrorCount = errorCount > MAX_ERROR_COUNT ? validationErrors.size()-(errorCount-MAX_ERROR_COUNT) : validationErrors.size();

                        // すべてのエラーメッセージを結合
                        validationErrors.stream().limit(currentRowErrorCount).forEach(error-> {
                            // エラーをTXTファイルに記録
                            txtWriter.println( "行：" + rowNumber + "、" + error.getMessage());
                        });

                        // エラー数が最大値（100）を超えた場合
                        if (errorCount > MAX_ERROR_COUNT) {
                            logger.warn("エラー数が上限{}に達したため、検証を中止しました", MAX_ERROR_COUNT);
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 予期しない検証エラー
                    logger.error("検証中に予期しないエラーが発生:" , e);
                    throw e;
                }
            }

            txtWriter.flush();

            // エラーがある場合のみS3にアップロード
            if (errorCount > 0) {
                // S3 URLを結果に追加
                String s3Key = uploadToS3(options, txtOutputStream.toByteArray());
                result.addStatistic("errorFileS3Key", s3Key);
                result.setFailedCount(errorCount);
                logger.info("エラー件数: {}", errorCount);
            }
        } catch (Exception e) {
            logger.error("レコードのチェック途中やエラーファイルの作成に失敗しました", e);
            throw e;
        }
    }

    /**
     * データがない場合のメッセージを書き込む
     */
    private void writeErrorFile(ImportOptions options,ImportResult result,String errorMessage) throws IOException {
        ByteArrayOutputStream txtOutputStream = new ByteArrayOutputStream();

        try (PrintWriter txtWriter = new PrintWriter(new OutputStreamWriter(txtOutputStream, StandardCharsets.UTF_8))) {
            txtWriter.println(errorMessage);
            txtWriter.flush();

            // S3 URLを結果に追加
            String s3Key = uploadToS3(options,txtOutputStream.toByteArray());
            result.addStatistic("errorFileS3Key", s3Key);
            result.setFailedCount(1);
            logger.info("エラー件数: {}", 1);
        } catch (Exception e) {
            logger.error("エラーファイルの作成に失敗しました", e);
            throw e;
        }
    }

    /**
     * S3にアップロード
     */
    private String uploadToS3(ImportOptions options,byte[] txtData) throws IOException {
        // エラーTXTファイルの準備
        String errorFileName = String.format("error_%s_%s.txt", options.getErrorFileName(), DateUtil.getCurrentDateTimeString_YYYYMMDDHHMMSSSSS());
        String fixedContent = "エラーが発生したため、アップロード処理を終了しました。\n" +
                              "正常レコードについてもテーブル登録は行われておりませんので、再度全量アップロードを行ってください。\n" +
                              "エラー内容の出力は、エラー100件目を検出した行までの分となり、以降のチェック処理は行われません。\n" +
                              "\n";

        // 固定文言とエラー内容を結合する
        ByteArrayOutputStream combinedStream = new ByteArrayOutputStream();
        combinedStream.write(fixedContent.getBytes(StandardCharsets.UTF_8));
        combinedStream.write(txtData);
        byte[] combinedData = combinedStream.toByteArray();

        try {
            InputStream inputStream = new ByteArrayInputStream(combinedData);
            String s3Key = "import-errors/" + errorFileName;

            // S3Serviceを使用してアップロード
            Map<String, Object> uploadResult = s3Service.uploadFileFromStream(
                    inputStream,
                    s3Key,
                    "text/plain; charset=UTF-8",
                    (long) combinedData.length,
                    new HashMap<>()
            );
            logger.info("エラーファイルをS3にアップロードしました: {}", s3Key);
            return s3Key;
        } catch (Exception uploadEx) {
            logger.error("エラーファイルのS3アップロードに失敗しました", uploadEx);
            throw uploadEx;
        }
    }

    /**
     * バッチを処理
     */
    protected void processBatch(
            List<Map<String, Object>> batch,
            Class<T> dtoClass,
            ImportOptions options,
            ImportResult result) {
        try {
            // ステップ1: DTOをマッピング
            List<T> dtos = batch.stream()
                    .map(rawData -> DTOMapper.mapToDTO(rawData, dtoClass))
                    .collect(Collectors.toList());

            // ステップ2: マスタデータ補完（必要な場合のみ）
            enrichMasterDataIfNeeded(dtos);

            // ステップ3: トランザクション内で処理実行
            executeWithJdbcTemplateTransaction(template -> {
                processBatchByMode(dtos, options, template, result);
                return null;
            });

        } catch (Exception e) {
            String errorMsg = "バッチの処理に失敗しました: " + e.getMessage();
            logger.error(errorMsg, e);
            throw e;
        }
    }
    /**
     * JdbcTemplateを使ってトランザクション内で処理を実行
     */
    private <R> void executeWithJdbcTemplateTransaction(JdbcTemplate.TransactionCallback<R> callback) {
        try (Connection conn = AuroraConnectionManager.getConnection();
             JdbcTemplate template = new JdbcTemplate(conn)) {
            template.executeInTransaction(callback);
        } catch (Exception e) {
            logger.error("バッチ処理エラー", e);
            throw new RuntimeException("バッチ処理に失敗したため、トランザクションをロールバックします", e);
        }
    }

    /**
     * 処理モードに応じた分岐処理
     */
    private void processBatchByMode(List<T> dtos, ImportOptions options,
                                    JdbcTemplate template, ImportResult result) throws SQLException {
        if (options.isDeleteInsertMode()) {
            processDeleteInsertMode(dtos, options, template, result);
        } else if (options.isUpsertMode()) {
            processUpsertMode(dtos, options, template, result);
        } else {
            List<Map<String, Object>> insertRecords = dtos.stream()
                    .map(dto -> DTOConverter.toDatabase(dto, true, options))
                    .collect(Collectors.toList());
            if (!insertRecords.isEmpty()) {
                executeBatchInsert(insertRecords, options, template, result);
            }
        }
    }

    /**
     * マスタデータ補完処理（必要な場合のみ実行）
     * 各DTOがマスタデータ補完を必要とする場合、サブクラスの実装を呼び出す
     *
     * @param dtos importのDTOクラス
     */
    private void enrichMasterDataIfNeeded(List<T> dtos) {
        // マスタデータ補完が必要なDTOをフィルタリング
        List<T> enrichmentNeededDtos = dtos.stream()
                .filter(DatabaseMappable::requiresMasterDataEnrichment)
                .collect(Collectors.toList());

        if (enrichmentNeededDtos.isEmpty()) {
            logger.debug("マスタデータ補完が必要なDTOはありません");
            return;
        }

        logger.debug("マスタデータ補完開始: 対象DTO数={}", enrichmentNeededDtos.size());

        try {
            // サブクラスの実装を呼び出してマスタデータを補完
            enrichMasterData(enrichmentNeededDtos, sessionCache);
            logger.debug("マスタデータ補完完了: 対象DTO数={}", enrichmentNeededDtos.size());
        } catch (Exception e) {
            String errorMsg = "マスタデータ補完中にエラーが発生しました: " + e.getMessage();
            logger.error(errorMsg, e);
            throw e;
        }
    }

    /**
     * マスタデータ補完の具体的な実装（サブクラスでオーバーライド）
     * デフォルト実装は何もしない
     *
     * @param dtos 補完対象のDTOリスト
     * @param cache セッションキャッシュ
     */
    protected void enrichMasterData(List<T> dtos,ImportSessionCache cache) {
        // デフォルト実装は何もしない
        // 各ImportServiceで必要に応じてオーバーライド
    }

    /**
     * 既存のUPSERTモード処理
     */
    private void processUpsertMode(List<T> dtos, ImportOptions options,
                                  JdbcTemplate template, ImportResult result) throws SQLException {

        // どのレコードが既に存在するか確認
        Set<String> existingKeys = new HashSet<>(checkExistingKeys(dtos, options, template));

        // DTOを更新用と挿入用に分ける
        List<Map<String, Object>> entitiesToInsert = new ArrayList<>();
        List<Map<String, Object>> entitiesToUpdate = new ArrayList<>();

        for (T dto : dtos) {
            // DTOから数据库字段への変换（静的メソッド使用、固定値フィールド注入対応）
            boolean isInsert = true;
            Map<String, Object> fields = DTOConverter.toDatabase(dto, isInsert, options);
            String keyStr = extractKeyString(fields, options);

            if (keyStr != null && existingKeys.contains(keyStr)) {
                // 更新用エンティティに追加
                Map<String, Object> updateFields = DTOConverter.toDatabase(dto, false, options);
                entitiesToUpdate.add(updateFields);
            } else {
                // 挿入用エンティティに追加
                entitiesToInsert.add(fields);
            }
        }

        // バッチ挿入を実行
        if (!entitiesToInsert.isEmpty()) {
            executeBatchInsert(entitiesToInsert, options, template, result);
        }

        // バッチ更新を実行
        if (!entitiesToUpdate.isEmpty()) {
            executeBatchUpdate(entitiesToUpdate, options, template, result);
        }
    }

    /**
     * 削除-挿入モードの処理
     */
    private void processDeleteInsertMode(List<T> dtos, ImportOptions options,
                                         JdbcTemplate template, ImportResult result) throws SQLException {

        DeleteConditionProvider<T> provider = options.getDeleteConditionProvider();
        if (provider == null) {
            throw new IllegalArgumentException("DELETE_INSERT モードでは DeleteConditionProvider の設定が必要です");
        }

        // 削除条件でグループ化
        Map<String, List<List<Object>>> deleteGroups = new HashMap<>();
        List<Map<String, Object>> insertRecords = new ArrayList<>(dtos.size());

        // データを処理して削除条件と挿入データを準備
        for (T dto : dtos) {
            // 削除条件を生成
            DeleteCondition deleteCondition = provider.generateDeleteCondition(dto, options.getTargetTable());

            if (deleteCondition != null && deleteCondition.isValid()) {
                deleteGroups.computeIfAbsent(deleteCondition.getDeleteSql(), k -> new ArrayList<>())
                        .add(deleteCondition.getParameters());
            }

            // 挿入データを準備
            insertRecords.add(DTOConverter.toDatabase(dto, true, options));
        }

        // バッチ削除
        int totalDeleted = 0;
        for (Map.Entry<String, List<List<Object>>> entry : deleteGroups.entrySet()) {
            Object[][] paramArray = entry.getValue().stream()
                    .map(List::toArray)
                    .toArray(Object[][]::new);

            int[] deleteResults = template.batchUpdate(entry.getKey(), Arrays.asList(paramArray));
            totalDeleted += Arrays.stream(deleteResults).sum();
        }
        // バッチ挿入を実行
        if (!insertRecords.isEmpty()) {
            executeBatchInsert(insertRecords, options, template, result);
        }

        // 結果を更新
        result.setDeletedCount(result.getDeletedCount() + totalDeleted);
        logger.debug("削除-挿入モード: {}件削除, {}件挿入", totalDeleted, insertRecords.size());
    }



    /**
     * バッチ挿入を実行 (JDBC版)
     */
    private void executeBatchInsert(
            List<Map<String, Object>> records,
            ImportOptions options,
            JdbcTemplate template,
            ImportResult result) throws SQLException {

        if (records.isEmpty()) return;

        // 最初のレコードからフィールド名を取得（順序保持）
        List<String> fieldNames = new ArrayList<>(records.getFirst().keySet());
        // INSERT文を構築
        String placeholders = String.join(",", Collections.nCopies(fieldNames.size(), "?"));
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)",
                options.getTargetTable(),
                String.join(",", fieldNames),
                placeholders);

        // バッチパラメータを準備
        List<Object[]> paramsList = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Object[] params = new Object[fieldNames.size()];
            for (int i = 0; i < fieldNames.size(); i++) {
                params[i] = record.get(fieldNames.get(i));
            }
            paramsList.add(params);
        }

        // バッチ挿入を実行
        int[] results = template.batchUpdate(sql, paramsList);
        int inserted = Arrays.stream(results).sum();
        result.setInsertedCount(result.getInsertedCount() + inserted);

        logger.debug("{}件のレコードをバッチ挿入しました", inserted);
    }

    /**
     * バッチ更新を実行 (JDBC版・複合主キーサポート)
     */
    private void executeBatchUpdate(
            List<Map<String, Object>> records,
            ImportOptions options,
            JdbcTemplate template,
            ImportResult result) throws SQLException {

        if (records.isEmpty()) return;

        // レコードをグループ化して効率化
        Map<Set<String>, List<Map<String, Object>>> recordsByFieldSet = new HashMap<>();

        for (Map<String, Object> record : records) {
            Set<String> fieldSet = new HashSet<>(record.keySet());
            // キー列を除外
            options.getKeyColumns().forEach(fieldSet::remove);

            recordsByFieldSet.computeIfAbsent(fieldSet, k -> new ArrayList<>()).add(record);
        }

        int updated = 0;

        // 各フィールドセットでグループ化されたレコードを処理
        for (Map.Entry<Set<String>, List<Map<String, Object>>> entry : recordsByFieldSet.entrySet()) {
            Set<String> fieldSet = entry.getKey();
            List<Map<String, Object>> groupRecords = entry.getValue();

            // UPDATE文を構築
            List<String> setClause = fieldSet.stream()
                    .map(field -> field + " = ?")
                    .collect(Collectors.toList());

            // WHERE句を構築（複合主キー対応）
            List<String> whereClause = options.getKeyColumns().stream()
                    .map(keyCol -> keyCol + " = ?")
                    .collect(Collectors.toList());

            String sql = String.format("UPDATE %s SET %s WHERE %s",
                    options.getTargetTable(),
                    String.join(", ", setClause),
                    String.join(" AND ", whereClause));

            // バッチパラメータを準備
            List<Object[]> paramsList = new ArrayList<>();
            for (Map<String, Object> record : groupRecords) {
                List<Object> params = new ArrayList<>();
                // SET句のパラメータ
                for (String field : fieldSet) {
                    params.add(record.get(field));
                }
                // WHERE句のパラメータ（複合主キー）
                for (String keyColumn : options.getKeyColumns()) {
                    params.add(record.get(keyColumn));
                }

                paramsList.add(params.toArray());
            }

            // バッチ更新を実行
            int[] results = template.batchUpdate(sql, paramsList);
            updated += Arrays.stream(results).sum();
        }

        result.setUpdatedCount(result.getUpdatedCount() + updated);
        logger.debug("{}件のレコードをバッチ更新しました", updated);
    }

    /**
     * どのレコードが既に存在するか確認 (JDBC版・複合主キーサポート・DTOベース)
     */
    protected List<String> checkExistingKeys(
            List<T> dtos,
            ImportOptions options,
            JdbcTemplate template) throws SQLException {

        if (options.getKeyColumns() == null || options.getKeyColumns().isEmpty()) {
            return new ArrayList<>();
        }

        // 全てのキーの組み合わせを抽出
        Map<String, List<Object>> keyValuesMap = new HashMap<>();
        List<String> keyStrings = new ArrayList<>();

        for (T dto : dtos) {
            Map<String, Object> fields = DTOConverter.toDatabase(dto, true, options);
            String keyStr = extractKeyString(fields, options);
            if (keyStr != null) {
                keyStrings.add(keyStr);

                // 各キー列の値を保存
                List<Object> keyValues = new ArrayList<>();
                for (String keyColumn : options.getKeyColumns()) {
                    keyValues.add(fields.get(keyColumn));
                }
                keyValuesMap.put(keyStr, keyValues);
            }
        }

        if (!keyStrings.isEmpty()) {
            // 存在するキーを取得（複合キーの場合）
            Set<String> existingKeySet = template.findExistingCompositeKeys(
                    options.getTargetTable(),
                    options.getKeyColumns(),
                    keyValuesMap
            );

            return new ArrayList<>(existingKeySet);
        }

        return new ArrayList<>();
    }

    /**
     * キーを抽出（複合主キーサポート）
     */
    protected String extractKeyString(Map<String, Object> fields, ImportOptions options) {
        if (options.getKeyColumns() == null || options.getKeyColumns().isEmpty()) {
            return null;
        }

        // 複合キーの場合は、各キー値を連結
        List<String> keyValues = new ArrayList<>();
        for (String keyColumn : options.getKeyColumns()) {
            Object value = fields.get(keyColumn);
            keyValues.add(value != null ? value.toString() : "null");
        }

        return String.join("_", keyValues);
    }

    /**
     * タイムアウトをチェックすべきかどうかを判断
     */
    protected boolean shouldCheckTimeout(int processedCount) {
        return processedCount % 1000 == 0;
    }

}