package com.ms.bp.shared.common.constants;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 未収区分を定義するEnum
 */
public enum MishuKubun {
    KIKAKU(1, "企画未収"),
    TOKUBETSU(2, "特別未収"),
    NENKEI(3, "年契未収"),
    SONOTA(4, "その他未収");

    private final int code;
    private final String text;

    MishuKubun(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    private static final Map<Integer, MishuKubun> LOOKUP_MAP = Stream.of(values())
            .collect(Collectors.toMap(MishuKubun::getCode, e -> e));

    /**
     * 数字コードから対応するEnum定数を取得します。
     * @param code 数字コード
     * @return 対応するMishuKubun。見つからない場合はnullを返します。
     */
    public static MishuKubun getByCode(int code) {
        return LOOKUP_MAP.get(code);
    }
}