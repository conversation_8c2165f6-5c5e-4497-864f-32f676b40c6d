package com.ms.bp.shared.common.db;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.SQLExceptionOverride;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ssm.SsmClient;
import software.amazon.awssdk.services.ssm.model.GetParameterRequest;
import software.amazon.awssdk.services.ssm.model.GetParameterResponse;
import software.amazon.awssdk.services.ssm.model.ParameterNotFoundException;
import software.amazon.jdbc.util.SqlState;
import com.ms.bp.shared.common.config.ConfigurationManager;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Aurora PostgreSQL接続管理クラス
 * AWS Lambda環境向けに最適化し、Secrets Managerと連携して AWS Advanced JDBC Wrapper を活用
 * JDBC接続を直接提供
 */
public class AuroraConnectionManager {
    private static final Logger logger = LoggerFactory.getLogger(AuroraConnectionManager.class);

    // シングルトンパターンのデータソース参照
    private static final AtomicReference<HikariDataSource> dataSourceRef = new AtomicReference<>();

    // 緊急時用ののデータソース参照
    private static final AtomicReference<HikariDataSource> dataSourceRefEmergency = new AtomicReference<>();

    // Lambda実行コンテキストの追跡用
    private static final Map<String, Long> invocationStartTimes = new HashMap<>();

    // 安全マージン時間（秒）、Lambdaタイムアウト前に接続をクローズ
    private static final int SAFETY_MARGIN = 10;

    // 設定管理器
    private static final ConfigurationManager config = ConfigurationManager.getInstance();

    // Systems Manager Parameter Store設定
    private static final String PARAMETER_PREFIX = config.getProperty("db.parameter.prefix", "/ms-bp/dev/standard/db");
    private static final boolean USE_PARAMETER_STORE = config.getProperty("db.use.parameter.store", "true").equals("true");

    // AWS リージョン
    private static final String REGION = config.getProperty("aws.region", "ap-northeast-1");

    // SSH隧道設定
    private static final boolean SSH_TUNNEL_ENABLED = config.getProperty("ssh.tunnel.enabled", "false").equals("true");

    private AuroraConnectionManager() {
        // プライベートコンストラクタ
    }

    /**
     * Lambda呼び出しの追跡を初期化
     * @param context Lambdaコンテキスト
     */
    public static void initInvocation(Context context) {
        if (context != null) {
            invocationStartTimes.put(context.getAwsRequestId(), System.currentTimeMillis());
        }
    }

    /**
     * Lambda呼び出しの追跡をクリーンアップ
     * @param context Lambdaコンテキスト
     */
    public static void cleanupInvocation(Context context) {
        if (context != null) {
            invocationStartTimes.remove(context.getAwsRequestId());
        }

        // コネクションプールを閉じるべきかチェック
        if (invocationStartTimes.isEmpty() && dataSourceRef.get() != null) {
            closeDataSource();
        }
    }

    /**
     * データソースを取得
     * @return HikariDataSourceインスタンス
     */
    public static HikariDataSource getDataSource() {
        if (dataSourceRef.get() == null) {
            synchronized (AuroraConnectionManager.class) {
                if (dataSourceRef.get() == null) {
                    HikariDataSource dataSource = new HikariDataSource(createDataSource());
                    dataSourceRef.set(dataSource);
                }
            }
        }

        return dataSourceRef.get();
    }

    /**
     * 緊急時用のデータソースを取得
     * @return HikariDataSourceインスタンス
     */
    public static HikariDataSource getDataSourceEmergency() {
        if (dataSourceRefEmergency.get() == null) {
            synchronized (AuroraConnectionManager.class) {
                if (dataSourceRefEmergency.get() == null) {
                    HikariConfig hikariConfig = createDataSource();
                    hikariConfig.setMaximumPoolSize(1);
                    hikariConfig.setMinimumIdle(1);// 接続リーク対策のため接続数を増加
                    HikariDataSource dataSource = new HikariDataSource(hikariConfig);
                    dataSourceRefEmergency.set(dataSource);
                }
            }
        }

        return dataSourceRefEmergency.get();
    }

    /**
     * データソースを作成
     */
    private static HikariConfig createDataSource() {
        HikariConfig config = new HikariConfig();

        // SSH隧道が有効な場合は隧道を確立
        if (SSH_TUNNEL_ENABLED) {
            setupSSHTunnel();
        }

        // データベース接続情報を取得（優先順位: Parameter Store > Secrets Manager > 設定ファイル）
        DbCredentials credentials = null;

        // 1. Parameter Storeから取得を試行
        if (USE_PARAMETER_STORE) {
            credentials = getDbCredentialsFromParameterStore();
            if (credentials != null) {
                logger.debug("Parameter Storeから接続情報を取得しました");
            }
        }

        // 2. どちらも取得できない場合は設定ファイルにフォールバック
        if (credentials == null) {
            logger.warn("Parameter Storeから接続情報を取得できませんでした。設定ファイルを使用します。");
            credentials = getDbCredentialsFromEnv();
        }

        // SSH隧道が有効な場合は接続先をローカルホストに変更
        if (SSH_TUNNEL_ENABLED && SSHTunnelManager.isTunnelActive()) {
            credentials.setHost("localhost");
            credentials.setPort(String.valueOf(SSHTunnelManager.getLocalPort()));
            logger.debug("SSH隧道経由でデータベースに接続します: localhost:{}", SSHTunnelManager.getLocalPort());
        }

        // JDBC URL設定（SSH隧道使用時は標準PostgreSQLドライバを使用）
        String jdbcUrl;
        String profile = System.getenv(BusinessConstants.ENV_PROFILE);
        if (BusinessConstants.DEFAULT_PROFILE.equals(profile) ||
                (SSH_TUNNEL_ENABLED && SSHTunnelManager.isTunnelActive()) || !USE_PARAMETER_STORE) {
            // 標準PostgreSQLドライバを使用
            jdbcUrl = String.format("jdbc:postgresql://%s:%s/%s",
                    credentials.getHost(), credentials.getPort(), credentials.getDbname());
            config.setDriverClassName("org.postgresql.Driver");
            logger.debug("標準PostgreSQLドライバを使用します");
        } else {
            // 標準PostgreSQLドライバを使用
            jdbcUrl = String.format("jdbc:postgresql://%s:%s/%s",
                    credentials.getHost(), credentials.getPort(), credentials.getDbname());
            config.setDriverClassName("org.postgresql.Driver");
            logger.debug("AWS JDBC Wrapperを使用します");
        }

        // HikariCP基本設定
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(credentials.getUsername());
        config.setPassword(credentials.getPassword());

        // Aurora PostgreSQL固有の設定
        config.addDataSourceProperty("serverName", credentials.getHost());
        config.addDataSourceProperty("portNumber", credentials.getPort());
        config.addDataSourceProperty("databaseName", credentials.getDbname());

        // AWS JDBC Wrapper プラグイン設定
        Properties dsProps = new Properties();
        dsProps.setProperty("wrapperPlugins", "failover,initialConnection,efm2");  // Enable failover and enhanced monitoring
        dsProps.setProperty("wrapperDialect", "aurora-pg");  // Aurora PostgreSQL dialect
        dsProps.setProperty("failoverTimeoutMs", "30000");  // 30 seconds failover timeout
        dsProps.setProperty("openConnectionRetryTimeoutMs", "10000");  // 10 seconds retry timeout for initial connection
        config.setDataSourceProperties(dsProps);

        // Lambda環境最適化設定
        String lambda = System.getenv(BusinessConstants.LAMBDA_ID);
        if (BusinessConstants.WORK_LAMBDA_ID.equals(lambda)) {
            config.setMaximumPoolSize(3);
        } else {
            config.setMaximumPoolSize(1);       // 接続リーク対策のため接続数を増加
        }
        config.setMinimumIdle(0);           // 最小アイドル接続数を0に
        config.setIdleTimeout(60000);       // アイドル接続を60秒後に解放
        config.setConnectionTimeout(600000);  // 接続タイムアウト600秒（10分）
        config.setMaxLifetime(890000);      // 接続の最大寿命は約15分

        // パフォーマンス最適化設定
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");

        // フェイルオーバー時の例外ハンドリング設定
        config.setExceptionOverrideClassName("com.ms.bp.shared.common.db.AuroraConnectionManager$HikariCPSQLExceptionOverride");

        // 接続有効性チェック
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(3000);  // 接続検証タイムアウト3秒

        logger.debug("AWS JDBC Wrapper を使用したデータソース設定が作成されました");

        return config;
    }

    /**
     * HikariCPのSQL例外オーバーライドクラス
     * AWS JDBC Wrapperのフェイルオーバー関連の例外を適切に処理
     */
    public static class HikariCPSQLExceptionOverride implements SQLExceptionOverride {
        public Override adjudicate(SQLException sqlException) {
            String sqlState = sqlException.getSQLState();
            if (sqlState != null && (
                    sqlState.equalsIgnoreCase(SqlState.COMMUNICATION_LINK_CHANGED.getState()) ||
                            sqlState.equalsIgnoreCase(SqlState.CONNECTION_FAILURE_DURING_TRANSACTION.getState()))) {
                return Override.DO_NOT_EVICT;
            } else {
                return Override.CONTINUE_EVICT;
            }
        }
    }

    /**
     * Systems Manager Parameter Storeから接続情報を取得
     * @return DBの認証情報
     */
    private static DbCredentials getDbCredentialsFromParameterStore() {
        if (PARAMETER_PREFIX == null || PARAMETER_PREFIX.isEmpty()) {
            logger.debug("Parameter Storeのプレフィックスが設定されていません");
            return null;
        }

        try {
            // Systems Managerクライアントを作成
            SsmClient ssmClient = SsmClient.builder()
                    .region(Region.of(REGION))
                    .build();

            // 各パラメータを取得
            DbCredentials credentials = new DbCredentials();

            // ホスト名を取得
            String host = getParameterValue(ssmClient, PARAMETER_PREFIX + "/host");
            if (host == null) {
                logger.warn("Parameter Store: hostパラメータが見つかりません");
                return null;
            }
            credentials.setHost(host);

            // データベース名を取得
            String dbName = getParameterValue(ssmClient, PARAMETER_PREFIX + "/DataBaseName");
            if (dbName == null) {
                logger.warn("Parameter Store: DataBaseNameパラメータが見つかりません");
                return null;
            }
            credentials.setDbname(dbName);

            // ユーザー名を取得
            String username = getParameterValue(ssmClient, PARAMETER_PREFIX + "/MasterUsername");
            if (username == null) {
                logger.warn("Parameter Store: MasterUsernameパラメータが見つかりません");
                return null;
            }
            credentials.setUsername(username);

            // パスワードを取得（SecureString）
            String password = getParameterValue(ssmClient, PARAMETER_PREFIX + "/MasterUserPassword", true);
            if (password == null) {
                logger.warn("Parameter Store: MasterUserPasswordパラメータが見つかりません");
                return null;
            }
            credentials.setPassword(password);

            // ポート番号はデフォルト値を使用（Parameter Storeに設定されていない場合）
            String port = getParameterValue(ssmClient, PARAMETER_PREFIX + "/port");
            credentials.setPort(port != null ? port : "5432");

            logger.debug("Parameter Storeから接続情報を正常に取得しました");
            return credentials;

        } catch (Exception e) {
            logger.error("Parameter Storeからの接続情報取得に失敗しました", e);
            return null;
        }
    }

    /**
     * Parameter Storeから単一パラメータ値を取得
     * @param ssmClient SSMクライアント
     * @param parameterName パラメータ名
     * @return パラメータ値
     */
    private static String getParameterValue(SsmClient ssmClient, String parameterName) {
        return getParameterValue(ssmClient, parameterName, false);
    }

    /**
     * Parameter Storeから単一パラメータ値を取得
     * @param ssmClient SSMクライアント
     * @param parameterName パラメータ名
     * @param withDecryption 復号化するかどうか（SecureStringの場合true）
     * @return パラメータ値
     */
    private static String getParameterValue(SsmClient ssmClient, String parameterName, boolean withDecryption) {
        try {
            GetParameterRequest request = GetParameterRequest.builder()
                    .name(parameterName)
                    .withDecryption(withDecryption)
                    .build();

            GetParameterResponse response = ssmClient.getParameter(request);
            String value = response.parameter().value();

            logger.debug("Parameter Store: {} を取得しました", parameterName);
            return value;

        } catch (ParameterNotFoundException e) {
            logger.debug("Parameter Store: {} が見つかりません", parameterName);
            return null;
        } catch (Exception e) {
            logger.error("Parameter Store: {} の取得中にエラーが発生しました", parameterName, e);
            return null;
        }
    }


    /**
     * SSH隧道を設定
     */
    private static void setupSSHTunnel() {
        try {
            // SSH隧道設定を初期化
            Properties tunnelConfig = new Properties();
            tunnelConfig.setProperty("ssh.tunnel.ssh.host", config.getProperty("ssh.tunnel.ssh.host", ""));
            tunnelConfig.setProperty("ssh.tunnel.ssh.port", config.getProperty("ssh.tunnel.ssh.port", "22"));
            tunnelConfig.setProperty("ssh.tunnel.ssh.username", config.getProperty("ssh.tunnel.ssh.username", ""));
            tunnelConfig.setProperty("ssh.tunnel.ssh.private.key.path", config.getProperty("ssh.tunnel.ssh.private.key.path", ""));
            tunnelConfig.setProperty("ssh.tunnel.local.port", config.getProperty("ssh.tunnel.local.port", "15432"));
            tunnelConfig.setProperty("ssh.tunnel.remote.host", config.getProperty("ssh.tunnel.remote.host", ""));
            tunnelConfig.setProperty("ssh.tunnel.remote.port", config.getProperty("ssh.tunnel.remote.port", "5432"));
            tunnelConfig.setProperty("ssh.tunnel.connection.timeout", config.getProperty("ssh.tunnel.connection.timeout", "30000"));
            tunnelConfig.setProperty("ssh.tunnel.keep.alive.interval", config.getProperty("ssh.tunnel.keep.alive.interval", "60000"));

            SSHTunnelManager.initializeConfig(tunnelConfig);

            // SSH隧道を確立
            if (!SSHTunnelManager.establishTunnel()) {
                logger.error("SSH隧道の確立に失敗しました。通常の接続にフォールバックします。");
            }
        } catch (Exception e) {
            logger.error("SSH隧道の設定中にエラーが発生しました", e);
        }
    }

    /**
     * 設定ファイルから接続情報を取得
     * @return DBの認証情報
     */
    private static DbCredentials getDbCredentialsFromEnv() {
        DbCredentials credentials = new DbCredentials();
        credentials.setHost(config.getProperty("db.host", "localhost"));
        credentials.setPort(config.getProperty("db.port", "5432"));
        credentials.setDbname(config.getProperty("db.name", "postgres"));
        credentials.setUsername(config.getProperty("db.username", "postgres"));
        credentials.setPassword(config.getProperty("db.password", "123456"));
        return credentials;
    }

    /**
     * データソースを閉じる
     */
    public static void closeDataSource() {
        HikariDataSource dataSource = dataSourceRef.getAndSet(null);
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("データソースが閉じられました");
        }

        // SSH隧道も閉じる
        if (SSH_TUNNEL_ENABLED) {
            SSHTunnelManager.closeTunnel();
        }
    }

    /**
     * JDBC接続を取得
     * @return Connectionインスタンス
     */
    public static Connection getConnection() throws SQLException {
        return getDataSource().getConnection();
    }

    /**
     * 緊急時用のJDBC接続を取得
     * @return Connectionインスタンス
     */
    public static Connection getConnectionEmergency() throws SQLException {
        return getDataSourceEmergency().getConnection();
    }

    /**
     * Lambda実行時間がタイムアウトに近いかチェック
     * @param context Lambdaコンテキスト
     * @return 実行時間がタイムアウトに近い場合はtrue
     */
    public static boolean isNearTimeout(Context context) {
        if (context == null) {
            return false;
        }

        Long startTime = invocationStartTimes.get(context.getAwsRequestId());
        if (startTime == null) {
            return false;
        }

        long remainingSeconds = context.getRemainingTimeInMillis() / 1000;

        // 残り時間が安全マージンより小さい場合、タイムアウトに近いと判断
        return remainingSeconds < SAFETY_MARGIN;
    }

    /**
     * DBの認証情報を保持するクラス
     */
    @Setter
    @Getter
    private static class DbCredentials {
        // ゲッターとセッター
        private String host;
        private String port;
        private String dbname;
        private String username;
        private String password;
    }
}