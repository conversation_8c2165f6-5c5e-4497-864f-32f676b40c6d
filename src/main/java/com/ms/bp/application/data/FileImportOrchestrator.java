package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.domain.file.areaoutlookplan.OutlookPlanImportService;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.indirectprofitmaker.IndirectProfitMakerImportService;
import com.ms.bp.domain.file.planmaster.PlanMasterImportService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.file.FileProcessingService;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.io.model.ImportResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * ファイルインポート協調器
 * インポート処理のビジネスロジック協調を担当
 */
public class FileImportOrchestrator {
    private static final Logger logger = LoggerFactory.getLogger(FileImportOrchestrator.class);

    private final FileProcessingService fileProcessingService;
    private final Map<String, AbstractImportService<?>> importServices;

    public FileImportOrchestrator() {
        this.fileProcessingService = new FileProcessingService();
        
        // インポートサービスの登録
        this.importServices = new HashMap<>();
        this.importServices.put(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE, new PlanMasterImportService()); // 次年度計画マスタ
        this.importServices.put(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE, new IndirectProfitMakerImportService()); // 間接利益メーカー別
        //TODO 今後の拡張:
        this.importServices.put(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE, new OutlookPlanImportService(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE)); // 見通し・計画_採算管理単位C別＜本社＞のコード
        this.importServices.put(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE, new OutlookPlanImportService(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE)); // 見通し・計画_採算管理単位C別＜エリア＞のコード

    }

    /**
     * インポート処理を実行
     * @param jobId ジョブID
     * @param importRequest インポートリクエスト
     * @param context Lambda実行コンテキスト
     * @return インポート結果
     */
    public ImportResult processImport(Long jobId, ImportRequest importRequest, Context context) {
        try {
            logger.info("インポート協調処理開始: jobId={}", jobId);

            // インポートサービス取得
            AbstractImportService<?> importService = getImportService(importRequest.getDataType());
            importService.setLambdaContext(context);

            // ファイル取得とインポート実行の協調
            ImportResult result = coordinateImportExecution(importRequest, importService);

            logger.info("インポート協調処理完了: jobId={}", jobId);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("インポート協調処理エラー: jobId={}", jobId, e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "インポート処理中にエラーが発生しました: " + e.getMessage());
        }
    }

    // ==================== ビジネスロジック ====================

    /**
     * インポート実行の協調
     * @param importRequest インポートリクエスト
     * @param importService インポートサービス
     * @return インポート結果
     */
    private ImportResult coordinateImportExecution(ImportRequest importRequest, AbstractImportService<?> importService) {
        // FileProcessingService に技術的処理を委譲
        return fileProcessingService.executeImport(importRequest, importService);
    }


    // ==================== ユーティリティメソッド ====================

    /**
     * インポートサービスを取得
     */
    private AbstractImportService<?> getImportService(String dataType) {
        AbstractImportService<?> service = importServices.get(dataType.toUpperCase());
        if (service == null) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "サポートされていないデータタイプ: " + dataType);
        }
        return service;
    }

}
