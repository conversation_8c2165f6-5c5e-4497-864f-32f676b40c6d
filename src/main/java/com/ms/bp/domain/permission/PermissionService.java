package com.ms.bp.domain.permission;

import com.ms.bp.domain.permission.model.*;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.util.PermissionCodeParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 権限ドメインサービス
 * 権限に関するビジネスロジックを実装する
 */
public class PermissionService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionService.class);
    private static final char HEAD_OFFICE_MARKER = 'H';
    private static final char AREA_MARKER = 'A';

    // 権限統合処理用の定数
    private static final String PERMISSION_ALLOWED = "0";  // 許可

    private final PermissionRepository permissionRepository;

    public PermissionService(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    /**
     * ユーザーの全権限を取得
     *
     * @param userInfo ユーザー情報
     * @return ユーザーの権限リスト
     */
    public List<UserPermissionInfo> getUserPermissions(UserInfo userInfo) {
        logger.info("ユーザー権限取得開始: 社員コード={}", userInfo.getShainCode());

        try {
            // 共通権限を取得（基礎権限として使用）
            var commonPermissions = permissionRepository.findCommonPermissions(
                    userInfo.getSystemOperationCompanyCode(),
                    userInfo.getUnitCode(),
                    userInfo.getAreaCode()
            );

            // 個人別権限設定マスタを取得
            var personalPermissions = permissionRepository.findPersonalPermissions(
                    userInfo.getShainCode(),
                    userInfo.getSystemOperationCompanyCode()
            );

            // 共通権限と個人権限を統合処理
            var mergedPermissions = mergePermissions(commonPermissions, personalPermissions);

            var result = deduplicatePermissions(mergedPermissions);

            logger.info("ユーザー権限取得完了: 社員コード={}, 権限数={}", userInfo.getShainCode(), result.size());

            return result;

        } catch (Exception e) {
            logger.error("ユーザー権限取得中にエラーが発生しました: 社員コード={}", userInfo.getShainCode(), e);
            throw new ServiceException(
                    GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(), "ユーザー権限取得処理でエラーが発生しました: " + e.getMessage()
            );
        }
    }

    /**
     * 共通権限と個人権限を統合
     *
     * 統合処理ルール：
     * 1. 共通権限リストを基礎権限として使用
     * 2. 個人別権限設定マスタの各レコードを処理：
     *    - 権限コード一致時（上書き処理）：
     *      - 許可フラグ = 許可(0)：権限を保持（処理なし）
     *      - 許可フラグ = 禁止(1)：権限リストから削除
     *    - 権限コード不一致時（追加処理）：権限リストに追加
     * 3. 統合完了後、直接parsePermissionCodeでUserPermissionInfoに変換
     *
     * @param commonPermissions 共通権限リスト
     * @param personalPermissions 個人権限リスト
     * @return 統合済みユーザー権限情報リスト
     */
    private List<UserPermissionInfo> mergePermissions(List<Permission> commonPermissions,
                                                                         List<PersonalPermission> personalPermissions) {

        // 空値チェック
        if (commonPermissions == null) {
            commonPermissions = Collections.emptyList();
        }
        if (personalPermissions == null) {
            personalPermissions = Collections.emptyList();
        }
        logger.debug("権限統合処理開始: 共通権限数={}, 個人権限数={}", commonPermissions.size(), personalPermissions.size());

        // 共通権限をSetに変換
        var permissionCodes = commonPermissions.stream()
                .map(Permission::getPermissionCode)
                .collect(Collectors.toCollection(HashSet::new));

        // 個人別権限設定マスタの各レコードを処理
        personalPermissions.forEach(personalPerm -> {
            var permissionCode = personalPerm.getPermissionCode();
            var isAllowed = PERMISSION_ALLOWED.equals(personalPerm.getPermissionFlag());

            if (permissionCodes.contains(permissionCode)) {
                // 上書き処理：禁止フラグの場合は削除
                if (!isAllowed) {
                    permissionCodes.remove(permissionCode);
                    logger.debug("権限削除: 権限コード={}", permissionCode);
                }
            } else if (isAllowed) {
                // 追加処理：許可フラグの場合のみ追加
                permissionCodes.add(permissionCode);
                logger.debug("権限追加: 権限コード={}", permissionCode);
            }
        });

        // 統合完了後、直接parsePermissionCodeでUserPermissionInfoに変換
        var result = permissionCodes.stream()
                .map(PermissionService::parsePermissionCode)
                .toList();

        logger.debug("権限統合処理完了: 統合後権限数={}", result.size());
        return result;
    }



    /**
     * 権限リストの重複を削除
     * 3桁目が'H'（本社）と'A'（エリア）の権限が共存する場合、'A'の権限を削除
     */
    public static List<UserPermissionInfo> deduplicatePermissions(List<UserPermissionInfo> permissions) {
        var allCodes = permissions.stream()
                .map(UserPermissionInfo::getPermissionCode)
                .collect(Collectors.toSet());

        boolean headOfficeFlg = allCodes.stream()
                .anyMatch(permissionCode -> permissionCode != null && (permissionCode.charAt(2) == HEAD_OFFICE_MARKER));

        return permissions.stream()
                .filter(info -> {
                    var code = info.getPermissionCode();
                    // 3桁目が'A'かつ対応する'H'権限が存在する場合は除外
                    if (headOfficeFlg){
                        return (code.charAt(2) == HEAD_OFFICE_MARKER);
                    }
                    return true;
                }).toList();
    }

    /**
     * 権限コードを解析し、UserPermissionInfoオブジェクトを生成
     * 共通の権限コード解析ロジックを使用してコード重複を排除
     *
     * @param permissionCode 権限コード
     * @return 解析済み権限情報
     */
    public static UserPermissionInfo parsePermissionCode(String permissionCode) {
        // 共通解析ロジックを使用
        PermissionCodeParser.ParsedPermissionCode parsed = PermissionCodeParser.parse(permissionCode);

        // UserPermissionInfoオブジェクトを構築
        var info = new UserPermissionInfo();
        info.setPermissionCode(parsed.getPermissionCode());
        info.setOperationDivision(parsed.getOperationDivision());
        info.setAreaPattern(parsed.getAreaPattern());
        info.setFileType(parsed.getFileTypeCode());

        return info;
    }

}