package com.ms.bp.domain.file.indirectprofitmaker.strategy;

import com.ms.bp.application.data.FileExportOrchestrator.ExportTask;
import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.repository.impl.AreaCodeRepositoryImpl;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 間接利益計画_メーカー別専用ファイル分割戦略
 * エリア別ファイル分割とSKSA特殊エリア処理を実装
 * 主な機能：
 * 1. エリア別ファイル分割（各エリアごとに個別ファイル生成）
 * 2. SKSA→実際エリアコード変換処理
 * 3. エリア名を含むファイル名生成
 */
public class IndirectProfitMakerFileSplitStrategy implements FileSplitStrategy {
    private static final Logger logger = LoggerFactory.getLogger(IndirectProfitMakerFileSplitStrategy.class);

    /**
     * エクスポートタスクを作成
     *
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return エクスポートタスクリスト
     */
    @Override
    public List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("間接利益計画_メーカー別エクスポートタスク作成開始: ユーザー={}", userInfo.getShainCode());

        // エリア名短縮漢字取得
        List<AreaInfo> areaInfoList = processAreaCode(exportRequest);

        logger.info("間接利益計画_メーカー別エリア別分割タスク作成開始: エリア数={}, ユーザー={}", 
                areaInfoList.size(), userInfo.getShainCode());

        List<ExportTask> tasks = areaInfoList.stream()
                .map(area -> {
                    String areaName = area.getAreaName();
                    if (areaName == null || areaName.trim().isEmpty()) {
                        logger.error("エリア名短縮漢字が設定されていません: エリアコード={}", area.getAreaCode());
                        return null;
                    }

                    // エリア別のエクスポートリクエストを作成
                    ExportRequest areaRequest = new ExportRequest();
                    areaRequest.setDataType(exportRequest.getDataType());
                    areaRequest.setArea(Collections.singletonList(area.getAreaCode()));
                    areaRequest.setHnshBashoKubun(exportRequest.getHnshBashoKubun());
                    areaRequest.setCtgryKubun(exportRequest.getCtgryKubun());

                    // ファイル名を生成
                    String fileName = generateAreaFileName(area);

                    // エクスポートタスクを作成
                    return new ExportTask(
                            BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE,
                            areaRequest,
                            BusinessConstants.INDIRECT_PROFIT_MAKER_DEFAULT,
                            fileName,
                            "間接利益計画＿メーカー別"
                    );
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        logger.info("間接利益計画_メーカー別エリア別分割タスク作成完了: タスク数={}, ユーザー={}", 
                tasks.size(), userInfo.getShainCode());
        return tasks;
    }

    /**
     * エリア別ファイル名を生成
     *
     * @param area エリア情報
     * @return ファイル名
     */
    private String generateAreaFileName(AreaInfo area) {
        return String.format("%s_%s_%s.csv",
                BusinessConstants.FILE_NAME_INDIRECT_PROFIT,
                area.getAreaName(),
                DateUtil.getCurrentDateTimeString_YYYYMMDDHHMM());
    }

    /**
     * エリア名短縮漢字取得
     * SKSAが含まれている場合、データベースから実際のエリアコードを取得して置換
     *
     * @param originalRequest 元のエクスポートリクエスト
     * @return 処理済みエリア情報リスト
     */
    private List<AreaInfo> processAreaCode(ExportRequest originalRequest) {
        List<String> originalAreaList = originalRequest.getArea();

        // データベースから実際のエリア情報を取得
        return LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
            AreaCodeRepositoryImpl areaCodeRepository = new AreaCodeRepositoryImpl(jdbcTemplate);
            return areaCodeRepository.findAreaInfosForMsth(
                    DateUtil.getNextFiscalYear(),
                    originalAreaList.contains(BusinessConstants.PROFIT_MANAGEMENT_PLANNING_AREA_CODE) ?
                            BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE : "",
                    originalAreaList);
        });
    }
}
