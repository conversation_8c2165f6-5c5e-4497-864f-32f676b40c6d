package com.ms.bp.domain.file.areaoutlookplan.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

/**
 * エリア_企業別 SQL構築戦略インターフェース
 * 異なるエクスポートタスクに対して異なるSQLを構築するための戦略パターン
 */
public class AreaOutlookPlanCompanyDataStrategy implements DataAccessStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AreaOutlookPlanCompanyDataStrategy.class);

    /**
     * エクスポートリクエストとユーザー情報に基づいてSQLクエリを構築
     *
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return SQLクエリとパラメータ
     */
    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("エリア_移管_企業別_SQL構築開始: データ区分={}, エリア={},ユーザー={}", exportRequest.getDataKubun(),exportRequest.getArea(),userInfo.getShainCode());

        // 年度パラメータの設定（DateUtilを使用して次年度を取得）
        String nendoParam = DateUtil.getNextFiscalYear();

        // SQLクエリとパラメータリストを構築
        SqlWithParams queryResult = buildAreaOutlookPlanQueryWithParams(nendoParam,exportRequest.getDataKubun().getFirst(), exportRequest.getArea().getFirst(), userInfo);

        logger.info("エリア_移管_企業別_SQL構築完了: 年度={}, ユーザー={}", nendoParam, userInfo.getShainCode());
        return queryResult;
    }

    /**
     * 戦略名を取得
     *
     * @return 戦略名
     */
    @Override
    public String getStrategyName() {
        return BusinessConstants.AREA_OUTLOOK_PLAN_K;
    }

    /**
     * 移管前後により異なる条件を構築
     *
     * @param nendoParam 年度
     * @param dataKubun データ区分
     * @param areaCode エリアコード
     * @param userInfo ユーザー情報
     * @return SQLクエリとパラメータ
     */
    private SqlWithParams buildAreaOutlookPlanQueryWithParams(String nendoParam,String dataKubun, String areaCode, UserInfo userInfo) {
        // パラメータリストを作成（順序重要）
        List<Object> paramList = new ArrayList<>();

        // 1. 年度パラメータを追加（WHERE句の最初のパラメータ）
        paramList.add(nendoParam);
        paramList.add(String.valueOf(Integer.parseInt(nendoParam) - 1));
        paramList.add(nendoParam);

        //・ユーザに紐づく権限情報を取得 のレスポンスによって、以下条件を追加付与する。
        //	レスポンス.ロールリスト(0).役職区分判定要否 = 1:要 だった場合
        //			レスポンス.ロールリスト(0).役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
        //					レスポンス.ロールリスト(0).グループコード　と一致する、かつ　レスポンス.ロールリスト(0).ユニットコードと一致するデータのみ抽出対象とする。
        //			上記以外
        //					レスポンス.ロールリスト(0).グループコード　と一致するデータのみ抽出対象とする。
        //	レスポンス.ロールリスト(0).役職区分判定要否 = 0:否 だった場合
        String userCondition = "";
        if(BusinessConstants.REQUIRE_POSITION_SPECIAL_CHECK.equals(userInfo.getPositionSpecialCheck())){
            userCondition = switch (Objects.isNull(userInfo.getPositionCode()) ? StringUtils.EMPTY : userInfo.getPositionCode()) {
                case BusinessConstants.POSITION_CODE_51, BusinessConstants.POSITION_CODE_61 ->
                        String.format("AND (jinedo_mst.group_code='%S' AND jinedo_mst.unit_code='%S') ", userInfo.getGroupCode(), userInfo.getUnitCode());
                default -> String.format("AND jinedo_mst.group_code='%S'", userInfo.getGroupCode());
            };
        }

        String sql="";
        // 移管前（当年度組織）
        if(BusinessConstants.DATAKUBUN_IKO_BEFORE.equals(dataKubun))
        {
            sql = buildAreaOutlookplanQueryWithPositionalParams(
                    "jinedo_mst.group_code",// フィールド/結合条件：次年度計画マスタ.グループコード
                    String.format(" jinedo_mst.area_code='%S' %S", areaCode, userCondition),// 検索条件：エリアコード
                    "'移管前'" // フィールド：ファイル情報2
            );
        }
        else
        {
            userCondition = userCondition.replace("group_code","tky_group_code").replace("unit_code","tky_unit_code");
            // 移管後（次年度組織）
            sql = buildAreaOutlookplanQueryWithPositionalParams(
                    "jinedo_mst.tky_group_code",// フィールド/結合条件：次年度計画マスタ.グループコード
                    // 検索条件：適用エリアコード = {パラメータ.エリアコード} AND (A　OR　B）
                    // A_次年度計画マスタ.エリアコード={パラメータ.エリアコード} AND 次年度計画マスタ.移管先エリアコード<>""
                    // B_次年度計画マスタ.移管先エリアコード = {パラメータ.エリアコード}
                    String.format(" jinedo_mst.tky_area_code='%S' %S", areaCode, userCondition),
                    "'移管後'" // フィールド：ファイル情報2
            );
        }

        // 順序保持のためLinkedHashMapを使用してパラメータマップを構築
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < paramList.size(); i++) {
            paramMap.put("param" + i, paramList.get(i));
        }

        logger.debug("構築されたパラメータ順序: {}", paramList);
        return new SqlWithParams(sql, paramMap);
    }

    /**
     * エリア_企業別_取得用SQLクエリを構築
     *
     * @param group_code グループコード
     * @param condition 条件
     * @param dataKubun データ区分
     * @return SQLクエリとパラメータ
     */
    private String buildAreaOutlookplanQueryWithPositionalParams(String group_code,String condition,String dataKubun){

        String sql1 = """
                -- エリア_企業別
                WITH
                    target_ssnkn AS (
                        SELECT DISTINCT jinedo_mst.ssnkn_tncd
                        FROM t_jinendo_kkk as jinedo_mst
                        WHERE jinedo_mst.nendo = ? AND %S
                    ),
                    ssnkn_jssk AS (
                       SELECT
                         ssnkn_tn_c_chkst_jssk.ssnkn_tncd,
                         ssnkn_tn_c_chkst_jssk.group_code,
                         ssnkn_tn_c_chkst_jssk.kanri_kk_nendo,
                         -- 1月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_1_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_1_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_1_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_1_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_1_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_1_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_1_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_1_tskm,

                         -- 2月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_2_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_2_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_2_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_2_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_2_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_2_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_2_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_2_tskm,

                         -- 3月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_3_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_3_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_3_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_3_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_3_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_3_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_3_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_3_tskm,

                         -- 4月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_4_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_4_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_4_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_4_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_4_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_4_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_4_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_4_tskm,

                         -- 5月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_5_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_5_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_5_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_5_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_5_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_5_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_5_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_5_tskm,

                         -- 6月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_6_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_6_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_6_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_6_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_6_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_6_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_6_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_6_tskm,

                         -- 7月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_7_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_7_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_7_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_7_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_7_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_7_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_7_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_7_tskm,

                         -- 8月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_8_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_8_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_8_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_8_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_8_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_8_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_8_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_8_tskm,

                         -- 9月
                         SUM(CASE WHEN togo__kubun = '01' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_urg_9_tskm,
                         SUM(CASE WHEN togo__kubun = '02' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_urg_9_tskm,
                         SUM(CASE WHEN togo__kubun = '03' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_hmpnt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '04' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_hmpnt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '05' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_shhr_rbt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '06' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_shhr_rbt_9_tskm,
                         SUM(CASE WHEN togo__kubun = '07' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_cntr_fee_9_tskm,
                         SUM(CASE WHEN togo__kubun = '08' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_cntr_fee_9_tskm,
                         SUM(CASE WHEN togo__kubun = '09' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_zaiko_rieki_9_tskm,
                         SUM(CASE WHEN togo__kubun = '10' THEN jssk_9_tskm ELSE 0 END) AS ssnkn_jssk_chks_rieki_9_tskm
                    FROM ssnkn_tn_c_chkst_jssk
                    INNER JOIN
                         target_ssnkn AS t ON ssnkn_tn_c_chkst_jssk.ssnkn_tncd = t.ssnkn_tncd
                    WHERE kanri_kk_nendo = ?
                        AND togo__kubun IN ('01', '02', '03', '04', '05', '06', '07', '08', '09', '10')
                    GROUP BY ssnkn_tn_c_chkst_jssk.kanri_kk_nendo, ssnkn_tn_c_chkst_jssk.ssnkn_tncd, ssnkn_tn_c_chkst_jssk.group_code
                 ),
                    
                     --採算管理単位C別データを取得
                     ssnkn_tncd_datas AS (
                       SELECT
                        --次年度計画マスタ.グループコード
                        %S AS group_code,
                        --次年度計画マスタ.採算管理単位コード
                        jinedo_mst.ssnkn_tncd,
                        --次年度計画マスタ.企業コード
                        jinedo_mst.kigyo_code,
                        --企業マスタ.企業名漢字
                        kigyo_mst.kgym_kanji,
                        --次年度計画マスタ.カテゴリコード
                        jinedo_mst.ctgry_code,
                        --カテゴリマスタ.カテゴリ名漢字
                        cat_mst.ctgry_mei_kanji,
                        --次年度計画マスタ.変更後取組区分
                        jinedo_mst.hnkg_trkm_kubun,
                        --エリア＿見通し・計画_採算管理単位C別.業態比率
                        area_ssnkn_tn_c.gyt_hrts,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_1_tskm, 0) AS kkk_zaiko_urg_1_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿１月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_1_tskm, 0) AS kkk_chks_urg_1_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_1_tskm, 0) AS kkk_zaiko_rieki_1_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿１月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_1_tskm, 0) AS kkk_chks_rieki_1_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿２月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_2_tskm, 0) AS kkk_zaiko_urg_2_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿２月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_2_tskm, 0) AS kkk_chks_urg_2_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿２月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_2_tskm, 0) AS kkk_zaiko_rieki_2_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿２月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_2_tskm, 0) AS kkk_chks_rieki_2_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿３月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_3_tskm, 0) AS kkk_zaiko_urg_3_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿３月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_3_tskm, 0) AS kkk_chks_urg_3_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿３月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_3_tskm, 0) AS kkk_zaiko_rieki_3_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿３月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_3_tskm, 0) AS kkk_chks_rieki_3_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿４月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_4_tskm, 0) AS kkk_zaiko_urg_4_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿４月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_4_tskm, 0) AS kkk_chks_urg_4_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿４月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_4_tskm, 0) AS kkk_zaiko_rieki_4_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿４月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_4_tskm, 0) AS kkk_chks_rieki_4_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿５月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_5_tskm, 0) AS kkk_zaiko_urg_5_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿５月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_5_tskm, 0) AS kkk_chks_urg_5_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿５月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_5_tskm, 0) AS kkk_zaiko_rieki_5_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿５月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_5_tskm, 0) AS kkk_chks_rieki_5_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿６月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_6_tskm, 0) AS kkk_zaiko_urg_6_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿６月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_6_tskm, 0) AS kkk_chks_urg_6_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿６月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_6_tskm, 0) AS kkk_zaiko_rieki_6_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿６月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_6_tskm, 0) AS kkk_chks_rieki_6_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿７月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_7_tskm, 0) AS kkk_zaiko_urg_7_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿７月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_7_tskm, 0) AS kkk_chks_urg_7_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿７月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_7_tskm, 0) AS kkk_zaiko_rieki_7_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿７月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_7_tskm, 0) AS kkk_chks_rieki_7_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿８月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_8_tskm, 0) AS kkk_zaiko_urg_8_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿８月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_8_tskm, 0) AS kkk_chks_urg_8_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿８月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_8_tskm, 0) AS kkk_zaiko_rieki_8_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿８月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_8_tskm, 0) AS kkk_chks_rieki_8_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿９月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_9_tskm, 0) AS kkk_zaiko_urg_9_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿９月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_9_tskm, 0) AS kkk_chks_urg_9_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿９月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_9_tskm, 0) AS kkk_zaiko_rieki_9_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿９月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_9_tskm, 0) AS kkk_chks_rieki_9_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１０月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_10_tskm, 0) AS kkk_zaiko_urg_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿１０月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_10_tskm, 0) AS kkk_chks_urg_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１０月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_10_tskm, 0) AS kkk_zaiko_rieki_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿１０月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_10_tskm, 0) AS kkk_chks_rieki_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１１月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_11_tskm, 0) AS kkk_zaiko_urg_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿１１月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_11_tskm, 0) AS kkk_chks_urg_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１１月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_11_tskm, 0) AS kkk_zaiko_rieki_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿１１月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_11_tskm, 0) AS kkk_chks_rieki_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫売上＿１２月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_12_tskm, 0) AS kkk_zaiko_urg_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送売上＿１２月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_urg_12_tskm, 0) AS kkk_chks_urg_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿在庫利益＿１２月目
                        COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_12_tskm, 0) AS kkk_zaiko_rieki_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.計画＿直送利益＿１２月目
                        COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_12_tskm, 0) AS kkk_chks_rieki_12_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_1_tskm, 0) AS ssnkn_jssk_zaiko_urg_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_1_tskm, 0) AS ssnkn_jssk_chks_urg_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_1_tskm, 0) AS ssnkn_jssk_zaiko_rieki_1_tskm,
                        -- 採算管理単位C別_直接_実績.１月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_1_tskm, 0) AS ssnkn_jssk_chks_rieki_1_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_2_tskm, 0) AS ssnkn_jssk_zaiko_urg_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_2_tskm, 0) AS ssnkn_jssk_chks_urg_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_2_tskm, 0) AS ssnkn_jssk_zaiko_rieki_2_tskm,
                        -- 採算管理単位C別_直接_実績.2 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_2_tskm, 0) AS ssnkn_jssk_chks_rieki_2_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_3_tskm, 0) AS ssnkn_jssk_zaiko_urg_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_3_tskm, 0) AS ssnkn_jssk_chks_urg_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_3_tskm, 0) AS ssnkn_jssk_zaiko_rieki_3_tskm,
                        -- 採算管理単位C別_直接_実績.3 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_3_tskm, 0) AS ssnkn_jssk_chks_rieki_3_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_4_tskm, 0) AS ssnkn_jssk_zaiko_urg_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_4_tskm, 0) AS ssnkn_jssk_chks_urg_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_4_tskm, 0) AS ssnkn_jssk_zaiko_rieki_4_tskm,
                        -- 採算管理単位C別_直接_実績.4 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_4_tskm, 0) AS ssnkn_jssk_chks_rieki_4_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_5_tskm, 0) AS ssnkn_jssk_zaiko_urg_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_5_tskm, 0) AS ssnkn_jssk_chks_urg_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_5_tskm, 0) AS ssnkn_jssk_zaiko_rieki_5_tskm,
                        -- 採算管理単位C別_直接_実績.5 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_5_tskm, 0) AS ssnkn_jssk_chks_rieki_5_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_6_tskm, 0) AS ssnkn_jssk_zaiko_urg_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_6_tskm, 0) AS ssnkn_jssk_chks_urg_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_6_tskm, 0) AS ssnkn_jssk_zaiko_rieki_6_tskm,
                        -- 採算管理単位C別_直接_実績.6 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_6_tskm, 0) AS ssnkn_jssk_chks_rieki_6_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_7_tskm, 0) AS ssnkn_jssk_zaiko_urg_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_7_tskm, 0) AS ssnkn_jssk_chks_urg_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_7_tskm, 0) AS ssnkn_jssk_zaiko_rieki_7_tskm,
                        -- 採算管理単位C別_直接_実績.7 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_7_tskm, 0) AS ssnkn_jssk_chks_rieki_7_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_8_tskm, 0) AS ssnkn_jssk_zaiko_urg_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_8_tskm, 0) AS ssnkn_jssk_chks_urg_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_8_tskm, 0) AS ssnkn_jssk_zaiko_rieki_8_tskm,
                        -- 採算管理単位C別_直接_実績.8 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_8_tskm, 0) AS ssnkn_jssk_chks_rieki_8_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_総売上高(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_9_tskm, 0) AS ssnkn_jssk_zaiko_urg_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_総売上高(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_9_tskm, 0) AS ssnkn_jssk_chks_urg_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_直接利益(在庫)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_9_tskm, 0) AS ssnkn_jssk_zaiko_rieki_9_tskm,
                        -- 採算管理単位C別_直接_実績.9 月目_直接利益(直送)
                        COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_9_tskm, 0) AS ssnkn_jssk_chks_rieki_9_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１０月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_10_tskm, 0) AS jssk_mtsh_zaiko_urg_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１０月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_10_tskm, 0) AS jssk_mtsh_chks_urg_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１０月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_10_tskm, 0) AS jssk_mtsh_zaiko_rieki_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１０月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_10_tskm, 0) AS jssk_mtsh_chks_rieki_10_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１１月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_11_tskm, 0) AS jssk_mtsh_zaiko_urg_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１１月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_11_tskm, 0) AS jssk_mtsh_chks_urg_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１１月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_11_tskm, 0) AS jssk_mtsh_zaiko_rieki_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１１月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_11_tskm, 0) AS jssk_mtsh_chks_rieki_11_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫売上＿１２月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_12_tskm, 0) AS jssk_mtsh_zaiko_urg_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送売上＿１２月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_12_tskm, 0) AS jssk_mtsh_chks_urg_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿在庫利益＿１２月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_12_tskm, 0) AS jssk_mtsh_zaiko_rieki_12_tskm,
                        -- エリア＿見通し・計画_採算管理単位C別.実績見通し＿直送利益＿１２月目
                        COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_12_tskm, 0) AS jssk_mtsh_chks_rieki_12_tskm,
                        -- 4月から3月までの計画総売上高の合計（在庫および直送の両方を含む）
                        (
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_1_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_1_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_2_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_2_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_3_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_3_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_4_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_4_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_5_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_5_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_6_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_6_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_7_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_7_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_8_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_8_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_9_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_9_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_10_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_10_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_11_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_11_tskm, 0) +
                          COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_12_tskm, 0) + COALESCE(area_ssnkn_tn_c.kkk_chks_urg_12_tskm, 0)
                        ) AS total_kkk_urg_kingaku
                    FROM t_jinendo_kkk AS jinedo_mst
                    -- 企業情報と関連付け
                    LEFT JOIN m_kigyomst AS kigyo_mst
                         ON kigyo_mst.kigyo_code = jinedo_mst.kigyo_code
                         AND kigyo_mst.shiyo_knsh_kubun = '0'
                     
                    -- カテゴリ情報と関連付け
                    LEFT JOIN LATERAL (
                        SELECT ctgry_code,ctgry_mei_kanji
                        FROM m_categorymst
                        WHERE ctgry_code = jinedo_mst.ctgry_code
                            AND shiyo_knsh_kubun = '0'
                        ORDER BY sub_ctgry_code
                        LIMIT 1
                    ) cat_mst ON true
                     
                    -- エリア＿見通し・計画_採算管理単位C別と関連付け
                    LEFT JOIN LATERAL (
                         SELECT *
                         FROM t_area_mtsh_kkk_ssnkn_tn_c
                         WHERE nendo= jinedo_mst.nendo
                         AND ssnkn_tncd = jinedo_mst.ssnkn_tncd
                         AND group_code = %S
                        ) area_ssnkn_tn_c ON true
                     
                    -- 採算管理単位C別_直接_実績と関連付け
                    LEFT JOIN ssnkn_jssk
                         ON ssnkn_jssk.ssnkn_tncd = jinedo_mst.ssnkn_tncd
                        AND (ssnkn_jssk.ssnkn_tncd <> '1118888' OR ssnkn_jssk.group_code = %S) -- 1118888の場合の処理

                    WHERE jinedo_mst.nendo = ? AND %S
                    AND GREATEST(
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_1_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_2_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_3_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_4_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_5_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_6_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_7_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_8_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_9_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_urg_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_urg_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_hmpnt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_hmpnt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_shhr_rbt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_shhr_rbt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_cntr_fee_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_cntr_fee_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_zaiko_rieki_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.kkk_chks_rieki_12_tskm,0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_1_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_2_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_3_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_4_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_5_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_6_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_7_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_8_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_urg_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_urg_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_hmpnt_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_hmpnt_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_shhr_rbt_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_shhr_rbt_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_cntr_fee_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_cntr_fee_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_zaiko_rieki_9_tskm, 0),
                         COALESCE(ssnkn_jssk.ssnkn_jssk_chks_rieki_9_tskm, 0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_10_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_11_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_urg_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_urg_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_hmpnt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_hmpnt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_shhr_rbt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_shhr_rbt_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_cntr_fee_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_cntr_fee_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_zaiko_rieki_12_tskm,0),
                         COALESCE(area_ssnkn_tn_c.jssk_mtsh_chks_rieki_12_tskm,0)
                      )>= 1
                     ),
                     
                      --4月から3月までの計画総売上高の合計で採算管理単位C別データをソート
                      ranked_ssnkn_tncd_total_kkk_urg_kingaku AS (
                        SELECT
                          kigyo_code,
                          kgym_kanji,
                          group_code,
                          ctgry_code,
                          ctgry_mei_kanji,
                          hnkg_trkm_kubun,
                          ssnkn_tncd,
                          total_kkk_urg_kingaku,
                          gyt_hrts,
                          ROW_NUMBER() OVER (
                            PARTITION BY kigyo_code, group_code, ctgry_code
                            ORDER BY total_kkk_urg_kingaku DESC,ssnkn_tncd
                          ) AS rank
                        FROM ssnkn_tncd_datas
                      ),
                     
                      --4月から3月までの計画総売上高の合計が最大となる採算管理単位C別データを取得
                      first_ssnkn_tncd_total_kkk_urg_kingaku AS (
                        SELECT
                          kigyo_code,
                          kgym_kanji,
                          group_code,
                          ctgry_code,
                          ctgry_mei_kanji,
                          hnkg_trkm_kubun,
                          ssnkn_tncd,
                          total_kkk_urg_kingaku,
                          gyt_hrts
                        FROM ranked_ssnkn_tncd_total_kkk_urg_kingaku
                        WHERE rank=1
                      ),
                     
                      --企業CD、ｸﾞﾙｰﾌﾟCD、ｶﾃｺﾞﾘで月毎の各「総売上高(在庫)、総売上高(直送)、直接利益(在庫)、直接利益(直送)」により、すべての採算管理単位C別データを集約
                      groupby_sum_cat_datas AS (
                        SELECT
                          kigyo_code,
                          group_code,
                          ctgry_code,
                          --計画
                          SUM(kkk_zaiko_urg_1_tskm) AS total_kkk_zaiko_urg_1_tskm,
                          SUM(kkk_chks_urg_1_tskm) AS total_kkk_chks_urg_1_tskm,
                          SUM(kkk_zaiko_rieki_1_tskm) AS total_kkk_zaiko_rieki_1_tskm,
                          SUM(kkk_chks_rieki_1_tskm) AS total_kkk_chks_rieki_1_tskm,
                          SUM(kkk_zaiko_urg_2_tskm) AS total_kkk_zaiko_urg_2_tskm,
                          SUM(kkk_chks_urg_2_tskm) AS total_kkk_chks_urg_2_tskm,
                          SUM(kkk_zaiko_rieki_2_tskm) AS total_kkk_zaiko_rieki_2_tskm,
                          SUM(kkk_chks_rieki_2_tskm) AS total_kkk_chks_rieki_2_tskm,
                          SUM(kkk_zaiko_urg_3_tskm) AS total_kkk_zaiko_urg_3_tskm,
                          SUM(kkk_chks_urg_3_tskm) AS total_kkk_chks_urg_3_tskm,
                          SUM(kkk_zaiko_rieki_3_tskm) AS total_kkk_zaiko_rieki_3_tskm,
                          SUM(kkk_chks_rieki_3_tskm) AS total_kkk_chks_rieki_3_tskm,
                          SUM(kkk_zaiko_urg_4_tskm) AS total_kkk_zaiko_urg_4_tskm,
                          SUM(kkk_chks_urg_4_tskm) AS total_kkk_chks_urg_4_tskm,
                          SUM(kkk_zaiko_rieki_4_tskm) AS total_kkk_zaiko_rieki_4_tskm,
                          SUM(kkk_chks_rieki_4_tskm) AS total_kkk_chks_rieki_4_tskm,
                          SUM(kkk_zaiko_urg_5_tskm) AS total_kkk_zaiko_urg_5_tskm,
                          SUM(kkk_chks_urg_5_tskm) AS total_kkk_chks_urg_5_tskm,
                          SUM(kkk_zaiko_rieki_5_tskm) AS total_kkk_zaiko_rieki_5_tskm,
                          SUM(kkk_chks_rieki_5_tskm) AS total_kkk_chks_rieki_5_tskm,
                          SUM(kkk_zaiko_urg_6_tskm) AS total_kkk_zaiko_urg_6_tskm,
                          SUM(kkk_chks_urg_6_tskm) AS total_kkk_chks_urg_6_tskm,
                          SUM(kkk_zaiko_rieki_6_tskm) AS total_kkk_zaiko_rieki_6_tskm,
                          SUM(kkk_chks_rieki_6_tskm) AS total_kkk_chks_rieki_6_tskm,
                          SUM(kkk_zaiko_urg_7_tskm) AS total_kkk_zaiko_urg_7_tskm,
                          SUM(kkk_chks_urg_7_tskm) AS total_kkk_chks_urg_7_tskm,
                          SUM(kkk_zaiko_rieki_7_tskm) AS total_kkk_zaiko_rieki_7_tskm,
                          SUM(kkk_chks_rieki_7_tskm) AS total_kkk_chks_rieki_7_tskm,
                          SUM(kkk_zaiko_urg_8_tskm) AS total_kkk_zaiko_urg_8_tskm,
                          SUM(kkk_chks_urg_8_tskm) AS total_kkk_chks_urg_8_tskm,
                          SUM(kkk_zaiko_rieki_8_tskm) AS total_kkk_zaiko_rieki_8_tskm,
                          SUM(kkk_chks_rieki_8_tskm) AS total_kkk_chks_rieki_8_tskm,
                          SUM(kkk_zaiko_urg_9_tskm) AS total_kkk_zaiko_urg_9_tskm,
                          SUM(kkk_chks_urg_9_tskm) AS total_kkk_chks_urg_9_tskm,
                          SUM(kkk_zaiko_rieki_9_tskm) AS total_kkk_zaiko_rieki_9_tskm,
                          SUM(kkk_chks_rieki_9_tskm) AS total_kkk_chks_rieki_9_tskm,
                          SUM(kkk_zaiko_urg_10_tskm) AS total_kkk_zaiko_urg_10_tskm,
                          SUM(kkk_chks_urg_10_tskm) AS total_kkk_chks_urg_10_tskm,
                          SUM(kkk_zaiko_rieki_10_tskm) AS total_kkk_zaiko_rieki_10_tskm,
                          SUM(kkk_chks_rieki_10_tskm) AS total_kkk_chks_rieki_10_tskm,
                          SUM(kkk_zaiko_urg_11_tskm) AS total_kkk_zaiko_urg_11_tskm,
                          SUM(kkk_chks_urg_11_tskm) AS total_kkk_chks_urg_11_tskm,
                          SUM(kkk_zaiko_rieki_11_tskm) AS total_kkk_zaiko_rieki_11_tskm,
                          SUM(kkk_chks_rieki_11_tskm) AS total_kkk_chks_rieki_11_tskm,
                          SUM(kkk_zaiko_urg_12_tskm) AS total_kkk_zaiko_urg_12_tskm,
                          SUM(kkk_chks_urg_12_tskm) AS total_kkk_chks_urg_12_tskm,
                          SUM(kkk_zaiko_rieki_12_tskm) AS total_kkk_zaiko_rieki_12_tskm,
                          SUM(kkk_chks_rieki_12_tskm) AS total_kkk_chks_rieki_12_tskm,
                          --実績
                          SUM(ssnkn_jssk_zaiko_urg_1_tskm) AS total_ssnkn_jssk_zaiko_urg_1_tskm,
                          SUM(ssnkn_jssk_chks_urg_1_tskm) AS total_ssnkn_jssk_chks_urg_1_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_1_tskm) AS total_ssnkn_jssk_zaiko_rieki_1_tskm,
                          SUM(ssnkn_jssk_chks_rieki_1_tskm) AS total_ssnkn_jssk_chks_rieki_1_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_2_tskm) AS total_ssnkn_jssk_zaiko_urg_2_tskm,
                          SUM(ssnkn_jssk_chks_urg_2_tskm) AS total_ssnkn_jssk_chks_urg_2_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_2_tskm) AS total_ssnkn_jssk_zaiko_rieki_2_tskm,
                          SUM(ssnkn_jssk_chks_rieki_2_tskm) AS total_ssnkn_jssk_chks_rieki_2_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_3_tskm) AS total_ssnkn_jssk_zaiko_urg_3_tskm,
                          SUM(ssnkn_jssk_chks_urg_3_tskm) AS total_ssnkn_jssk_chks_urg_3_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_3_tskm) AS total_ssnkn_jssk_zaiko_rieki_3_tskm,
                          SUM(ssnkn_jssk_chks_rieki_3_tskm) AS total_ssnkn_jssk_chks_rieki_3_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_4_tskm) AS total_ssnkn_jssk_zaiko_urg_4_tskm,
                          SUM(ssnkn_jssk_chks_urg_4_tskm) AS total_ssnkn_jssk_chks_urg_4_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_4_tskm) AS total_ssnkn_jssk_zaiko_rieki_4_tskm,
                          SUM(ssnkn_jssk_chks_rieki_4_tskm) AS total_ssnkn_jssk_chks_rieki_4_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_5_tskm) AS total_ssnkn_jssk_zaiko_urg_5_tskm,
                          SUM(ssnkn_jssk_chks_urg_5_tskm) AS total_ssnkn_jssk_chks_urg_5_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_5_tskm) AS total_ssnkn_jssk_zaiko_rieki_5_tskm,
                          SUM(ssnkn_jssk_chks_rieki_5_tskm) AS total_ssnkn_jssk_chks_rieki_5_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_6_tskm) AS total_ssnkn_jssk_zaiko_urg_6_tskm,
                          SUM(ssnkn_jssk_chks_urg_6_tskm) AS total_ssnkn_jssk_chks_urg_6_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_6_tskm) AS total_ssnkn_jssk_zaiko_rieki_6_tskm,
                          SUM(ssnkn_jssk_chks_rieki_6_tskm) AS total_ssnkn_jssk_chks_rieki_6_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_7_tskm) AS total_ssnkn_jssk_zaiko_urg_7_tskm,
                          SUM(ssnkn_jssk_chks_urg_7_tskm) AS total_ssnkn_jssk_chks_urg_7_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_7_tskm) AS total_ssnkn_jssk_zaiko_rieki_7_tskm,
                          SUM(ssnkn_jssk_chks_rieki_7_tskm) AS total_ssnkn_jssk_chks_rieki_7_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_8_tskm) AS total_ssnkn_jssk_zaiko_urg_8_tskm,
                          SUM(ssnkn_jssk_chks_urg_8_tskm) AS total_ssnkn_jssk_chks_urg_8_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_8_tskm) AS total_ssnkn_jssk_zaiko_rieki_8_tskm,
                          SUM(ssnkn_jssk_chks_rieki_8_tskm) AS total_ssnkn_jssk_chks_rieki_8_tskm,
                          SUM(ssnkn_jssk_zaiko_urg_9_tskm) AS total_ssnkn_jssk_zaiko_urg_9_tskm,
                          SUM(ssnkn_jssk_chks_urg_9_tskm) AS total_ssnkn_jssk_chks_urg_9_tskm,
                          SUM(ssnkn_jssk_zaiko_rieki_9_tskm) AS total_ssnkn_jssk_zaiko_rieki_9_tskm,
                          SUM(ssnkn_jssk_chks_rieki_9_tskm) AS total_ssnkn_jssk_chks_rieki_9_tskm,
                          SUM(jssk_mtsh_zaiko_urg_10_tskm) AS total_jssk_mtsh_zaiko_urg_10_tskm,
                          SUM(jssk_mtsh_chks_urg_10_tskm) AS total_jssk_mtsh_chks_urg_10_tskm,
                          SUM(jssk_mtsh_zaiko_rieki_10_tskm) AS total_jssk_mtsh_zaiko_rieki_10_tskm,
                          SUM(jssk_mtsh_chks_rieki_10_tskm) AS total_jssk_mtsh_chks_rieki_10_tskm,
                          SUM(jssk_mtsh_zaiko_urg_11_tskm) AS total_jssk_mtsh_zaiko_urg_11_tskm,
                          SUM(jssk_mtsh_chks_urg_11_tskm) AS total_jssk_mtsh_chks_urg_11_tskm,
                          SUM(jssk_mtsh_zaiko_rieki_11_tskm) AS total_jssk_mtsh_zaiko_rieki_11_tskm,
                          SUM(jssk_mtsh_chks_rieki_11_tskm) AS total_jssk_mtsh_chks_rieki_11_tskm,
                          SUM(jssk_mtsh_zaiko_urg_12_tskm) AS total_jssk_mtsh_zaiko_urg_12_tskm,
                          SUM(jssk_mtsh_chks_urg_12_tskm) AS total_jssk_mtsh_chks_urg_12_tskm,
                          SUM(jssk_mtsh_zaiko_rieki_12_tskm) AS total_jssk_mtsh_zaiko_rieki_12_tskm,
                          SUM(jssk_mtsh_chks_rieki_12_tskm) AS total_jssk_mtsh_chks_rieki_12_tskm,
                          -- 4月から3月までの計画総売上高の合計（在庫および直送の両方を含む）
                          SUM(
                               kkk_zaiko_urg_1_tskm + kkk_chks_urg_1_tskm +
                               kkk_zaiko_urg_2_tskm + kkk_chks_urg_2_tskm +
                               kkk_zaiko_urg_3_tskm + kkk_chks_urg_3_tskm +
                               kkk_zaiko_urg_4_tskm + kkk_chks_urg_4_tskm +
                               kkk_zaiko_urg_5_tskm + kkk_chks_urg_5_tskm +
                               kkk_zaiko_urg_6_tskm + kkk_chks_urg_6_tskm +
                               kkk_zaiko_urg_7_tskm + kkk_chks_urg_7_tskm +
                               kkk_zaiko_urg_8_tskm + kkk_chks_urg_8_tskm +
                               kkk_zaiko_urg_9_tskm + kkk_chks_urg_9_tskm +
                               kkk_zaiko_urg_10_tskm + kkk_chks_urg_10_tskm +
                               kkk_zaiko_urg_11_tskm + kkk_chks_urg_11_tskm +
                               kkk_zaiko_urg_12_tskm + kkk_chks_urg_12_tskm
                             ) AS total_all_ssnkn_tncd_kkk_urg_kingaku
                        FROM ssnkn_tncd_datas
                        GROUP BY
                            kigyo_code,
                            group_code,
                            ctgry_code
                      )
                    """;

        String sql2 = """
                  SELECT
                     LPAD( (ROW_NUMBER() OVER (ORDER BY groupby_data.total_all_ssnkn_tncd_kkk_urg_kingaku DESC,groupby_data.kigyo_code, groupby_data.group_code, groupby_data.ctgry_code))::text, 4, '0' ) AS no,
                     first_data.kgym_kanji,
                     first_data.ctgry_mei_kanji,
                     first_data.hnkg_trkm_kubun,
                     first_data.gyt_hrts,
                     groupby_data.kigyo_code,
                     groupby_data.group_code,
                     groupby_data.ctgry_code,
                     --計画
                     ROUND(total_kkk_zaiko_urg_1_tskm / 1000.0) AS total_kkk_zaiko_urg_1_tskm,
                     ROUND(total_kkk_chks_urg_1_tskm / 1000.0) AS total_kkk_chks_urg_1_tskm,
                     ROUND(total_kkk_zaiko_rieki_1_tskm / 1000.0) AS total_kkk_zaiko_rieki_1_tskm,
                     ROUND(total_kkk_chks_rieki_1_tskm / 1000.0) AS total_kkk_chks_rieki_1_tskm,
                     ROUND(total_kkk_zaiko_urg_2_tskm / 1000.0) AS total_kkk_zaiko_urg_2_tskm,
                     ROUND(total_kkk_chks_urg_2_tskm / 1000.0) AS total_kkk_chks_urg_2_tskm,
                     ROUND(total_kkk_zaiko_rieki_2_tskm / 1000.0) AS total_kkk_zaiko_rieki_2_tskm,
                     ROUND(total_kkk_chks_rieki_2_tskm / 1000.0) AS total_kkk_chks_rieki_2_tskm,
                     ROUND(total_kkk_zaiko_urg_3_tskm / 1000.0) AS total_kkk_zaiko_urg_3_tskm,
                     ROUND(total_kkk_chks_urg_3_tskm / 1000.0) AS total_kkk_chks_urg_3_tskm,
                     ROUND(total_kkk_zaiko_rieki_3_tskm / 1000.0) AS total_kkk_zaiko_rieki_3_tskm,
                     ROUND(total_kkk_chks_rieki_3_tskm / 1000.0) AS total_kkk_chks_rieki_3_tskm,
                     ROUND(total_kkk_zaiko_urg_4_tskm / 1000.0) AS total_kkk_zaiko_urg_4_tskm,
                     ROUND(total_kkk_chks_urg_4_tskm / 1000.0) AS total_kkk_chks_urg_4_tskm,
                     ROUND(total_kkk_zaiko_rieki_4_tskm / 1000.0) AS total_kkk_zaiko_rieki_4_tskm,
                     ROUND(total_kkk_chks_rieki_4_tskm / 1000.0) AS total_kkk_chks_rieki_4_tskm,
                     ROUND(total_kkk_zaiko_urg_5_tskm / 1000.0) AS total_kkk_zaiko_urg_5_tskm,
                     ROUND(total_kkk_chks_urg_5_tskm / 1000.0) AS total_kkk_chks_urg_5_tskm,
                     ROUND(total_kkk_zaiko_rieki_5_tskm / 1000.0) AS total_kkk_zaiko_rieki_5_tskm,
                     ROUND(total_kkk_chks_rieki_5_tskm / 1000.0) AS total_kkk_chks_rieki_5_tskm,
                     ROUND(total_kkk_zaiko_urg_6_tskm / 1000.0) AS total_kkk_zaiko_urg_6_tskm,
                     ROUND(total_kkk_chks_urg_6_tskm / 1000.0) AS total_kkk_chks_urg_6_tskm,
                     ROUND(total_kkk_zaiko_rieki_6_tskm / 1000.0) AS total_kkk_zaiko_rieki_6_tskm,
                     ROUND(total_kkk_chks_rieki_6_tskm / 1000.0) AS total_kkk_chks_rieki_6_tskm,
                     ROUND(total_kkk_zaiko_urg_7_tskm / 1000.0) AS total_kkk_zaiko_urg_7_tskm,
                     ROUND(total_kkk_chks_urg_7_tskm / 1000.0) AS total_kkk_chks_urg_7_tskm,
                     ROUND(total_kkk_zaiko_rieki_7_tskm / 1000.0) AS total_kkk_zaiko_rieki_7_tskm,
                     ROUND(total_kkk_chks_rieki_7_tskm / 1000.0) AS total_kkk_chks_rieki_7_tskm,
                     ROUND(total_kkk_zaiko_urg_8_tskm / 1000.0) AS total_kkk_zaiko_urg_8_tskm,
                     ROUND(total_kkk_chks_urg_8_tskm / 1000.0) AS total_kkk_chks_urg_8_tskm,
                     ROUND(total_kkk_zaiko_rieki_8_tskm / 1000.0) AS total_kkk_zaiko_rieki_8_tskm,
                     ROUND(total_kkk_chks_rieki_8_tskm / 1000.0) AS total_kkk_chks_rieki_8_tskm,
                     ROUND(total_kkk_zaiko_urg_9_tskm / 1000.0) AS total_kkk_zaiko_urg_9_tskm,
                     ROUND(total_kkk_chks_urg_9_tskm / 1000.0) AS total_kkk_chks_urg_9_tskm,
                     ROUND(total_kkk_zaiko_rieki_9_tskm / 1000.0) AS total_kkk_zaiko_rieki_9_tskm,
                     ROUND(total_kkk_chks_rieki_9_tskm / 1000.0) AS total_kkk_chks_rieki_9_tskm,
                     ROUND(total_kkk_zaiko_urg_10_tskm / 1000.0) AS total_kkk_zaiko_urg_10_tskm,
                     ROUND(total_kkk_chks_urg_10_tskm / 1000.0) AS total_kkk_chks_urg_10_tskm,
                     ROUND(total_kkk_zaiko_rieki_10_tskm / 1000.0) AS total_kkk_zaiko_rieki_10_tskm,
                     ROUND(total_kkk_chks_rieki_10_tskm / 1000.0) AS total_kkk_chks_rieki_10_tskm,
                     ROUND(total_kkk_zaiko_urg_11_tskm / 1000.0) AS total_kkk_zaiko_urg_11_tskm,
                     ROUND(total_kkk_chks_urg_11_tskm / 1000.0) AS total_kkk_chks_urg_11_tskm,
                     ROUND(total_kkk_zaiko_rieki_11_tskm / 1000.0) AS total_kkk_zaiko_rieki_11_tskm,
                     ROUND(total_kkk_chks_rieki_11_tskm / 1000.0) AS total_kkk_chks_rieki_11_tskm,
                     ROUND(total_kkk_zaiko_urg_12_tskm / 1000.0) AS total_kkk_zaiko_urg_12_tskm,
                     ROUND(total_kkk_chks_urg_12_tskm / 1000.0) AS total_kkk_chks_urg_12_tskm,
                     ROUND(total_kkk_zaiko_rieki_12_tskm / 1000.0) AS total_kkk_zaiko_rieki_12_tskm,
                     ROUND(total_kkk_chks_rieki_12_tskm / 1000.0) AS total_kkk_chks_rieki_12_tskm,
                     --実績
                     total_ssnkn_jssk_zaiko_urg_1_tskm,
                     total_ssnkn_jssk_chks_urg_1_tskm,
                     total_ssnkn_jssk_zaiko_rieki_1_tskm,
                     total_ssnkn_jssk_chks_rieki_1_tskm,
                     total_ssnkn_jssk_zaiko_urg_2_tskm,
                     total_ssnkn_jssk_chks_urg_2_tskm,
                     total_ssnkn_jssk_zaiko_rieki_2_tskm,
                     total_ssnkn_jssk_chks_rieki_2_tskm,
                     total_ssnkn_jssk_zaiko_urg_3_tskm,
                     total_ssnkn_jssk_chks_urg_3_tskm,
                     total_ssnkn_jssk_zaiko_rieki_3_tskm,
                     total_ssnkn_jssk_chks_rieki_3_tskm,
                     total_ssnkn_jssk_zaiko_urg_4_tskm,
                     total_ssnkn_jssk_chks_urg_4_tskm,
                     total_ssnkn_jssk_zaiko_rieki_4_tskm,
                     total_ssnkn_jssk_chks_rieki_4_tskm,
                     total_ssnkn_jssk_zaiko_urg_5_tskm,
                     total_ssnkn_jssk_chks_urg_5_tskm,
                     total_ssnkn_jssk_zaiko_rieki_5_tskm,
                     total_ssnkn_jssk_chks_rieki_5_tskm,
                     total_ssnkn_jssk_zaiko_urg_6_tskm,
                     total_ssnkn_jssk_chks_urg_6_tskm,
                     total_ssnkn_jssk_zaiko_rieki_6_tskm,
                     total_ssnkn_jssk_chks_rieki_6_tskm,
                     total_ssnkn_jssk_zaiko_urg_7_tskm,
                     total_ssnkn_jssk_chks_urg_7_tskm,
                     total_ssnkn_jssk_zaiko_rieki_7_tskm,
                     total_ssnkn_jssk_chks_rieki_7_tskm,
                     total_ssnkn_jssk_zaiko_urg_8_tskm,
                     total_ssnkn_jssk_chks_urg_8_tskm,
                     total_ssnkn_jssk_zaiko_rieki_8_tskm,
                     total_ssnkn_jssk_chks_rieki_8_tskm,
                     total_ssnkn_jssk_zaiko_urg_9_tskm,
                     total_ssnkn_jssk_chks_urg_9_tskm,
                     total_ssnkn_jssk_zaiko_rieki_9_tskm,
                     total_ssnkn_jssk_chks_rieki_9_tskm,
                     ROUND(total_jssk_mtsh_zaiko_urg_10_tskm / 1000.0) AS total_jssk_mtsh_zaiko_urg_10_tskm,
                     ROUND(total_jssk_mtsh_chks_urg_10_tskm / 1000.0) AS total_jssk_mtsh_chks_urg_10_tskm,
                     ROUND(total_jssk_mtsh_zaiko_rieki_10_tskm / 1000.0) AS total_jssk_mtsh_zaiko_rieki_10_tskm,
                     ROUND(total_jssk_mtsh_chks_rieki_10_tskm / 1000.0) AS total_jssk_mtsh_chks_rieki_10_tskm,
                     ROUND(total_jssk_mtsh_zaiko_urg_11_tskm / 1000.0) AS total_jssk_mtsh_zaiko_urg_11_tskm,
                     ROUND(total_jssk_mtsh_chks_urg_11_tskm / 1000.0) AS total_jssk_mtsh_chks_urg_11_tskm,
                     ROUND(total_jssk_mtsh_zaiko_rieki_11_tskm / 1000.0) AS total_jssk_mtsh_zaiko_rieki_11_tskm,
                     ROUND(total_jssk_mtsh_chks_rieki_11_tskm / 1000.0) AS total_jssk_mtsh_chks_rieki_11_tskm,
                     ROUND(total_jssk_mtsh_zaiko_urg_12_tskm / 1000.0) AS total_jssk_mtsh_zaiko_urg_12_tskm,
                     ROUND(total_jssk_mtsh_chks_urg_12_tskm / 1000.0) AS total_jssk_mtsh_chks_urg_12_tskm,
                     ROUND(total_jssk_mtsh_zaiko_rieki_12_tskm / 1000.0) AS total_jssk_mtsh_zaiko_rieki_12_tskm,
                     ROUND(total_jssk_mtsh_chks_rieki_12_tskm / 1000.0) AS total_jssk_mtsh_chks_rieki_12_tskm,
                     '見通し・計画_企業別<エリア>' AS file_info_1,
                     %S AS file_info_2
                  FROM groupby_sum_cat_datas AS groupby_data
                  LEFT join first_ssnkn_tncd_total_kkk_urg_kingaku AS first_data
                  ON groupby_data.kigyo_code=first_data.kigyo_code
                  AND groupby_data.group_code=first_data.group_code
                  AND groupby_data.ctgry_code=first_data.ctgry_code
                  WHERE GREATEST(
                       total_kkk_zaiko_urg_1_tskm,
                       total_kkk_chks_urg_1_tskm,
                       total_kkk_zaiko_rieki_1_tskm,
                       total_kkk_chks_rieki_1_tskm,
                       total_kkk_zaiko_urg_2_tskm,
                       total_kkk_chks_urg_2_tskm,
                       total_kkk_zaiko_rieki_2_tskm,
                       total_kkk_chks_rieki_2_tskm,
                       total_kkk_zaiko_urg_3_tskm,
                       total_kkk_chks_urg_3_tskm,
                       total_kkk_zaiko_rieki_3_tskm,
                       total_kkk_chks_rieki_3_tskm,
                       total_kkk_zaiko_urg_4_tskm,
                       total_kkk_chks_urg_4_tskm,
                       total_kkk_zaiko_rieki_4_tskm,
                       total_kkk_chks_rieki_4_tskm,
                       total_kkk_zaiko_urg_5_tskm,
                       total_kkk_chks_urg_5_tskm,
                       total_kkk_zaiko_rieki_5_tskm,
                       total_kkk_chks_rieki_5_tskm,
                       total_kkk_zaiko_urg_6_tskm,
                       total_kkk_chks_urg_6_tskm,
                       total_kkk_zaiko_rieki_6_tskm,
                       total_kkk_chks_rieki_6_tskm,
                       total_kkk_zaiko_urg_7_tskm,
                       total_kkk_chks_urg_7_tskm,
                       total_kkk_zaiko_rieki_7_tskm,
                       total_kkk_chks_rieki_7_tskm,
                       total_kkk_zaiko_urg_8_tskm,
                       total_kkk_chks_urg_8_tskm,
                       total_kkk_zaiko_rieki_8_tskm,
                       total_kkk_chks_rieki_8_tskm,
                       total_kkk_zaiko_urg_9_tskm,
                       total_kkk_chks_urg_9_tskm,
                       total_kkk_zaiko_rieki_9_tskm,
                       total_kkk_chks_rieki_9_tskm,
                       total_kkk_zaiko_urg_10_tskm,
                       total_kkk_chks_urg_10_tskm,
                       total_kkk_zaiko_rieki_10_tskm,
                       total_kkk_chks_rieki_10_tskm,
                       total_kkk_zaiko_urg_11_tskm,
                       total_kkk_chks_urg_11_tskm,
                       total_kkk_zaiko_rieki_11_tskm,
                       total_kkk_chks_rieki_11_tskm,
                       total_kkk_zaiko_urg_12_tskm,
                       total_kkk_chks_urg_12_tskm,
                       total_kkk_zaiko_rieki_12_tskm,
                       total_kkk_chks_rieki_12_tskm,
                       total_ssnkn_jssk_zaiko_urg_1_tskm,
                       total_ssnkn_jssk_chks_urg_1_tskm,
                       total_ssnkn_jssk_zaiko_rieki_1_tskm,
                       total_ssnkn_jssk_chks_rieki_1_tskm,
                       total_ssnkn_jssk_zaiko_urg_2_tskm,
                       total_ssnkn_jssk_chks_urg_2_tskm,
                       total_ssnkn_jssk_zaiko_rieki_2_tskm,
                       total_ssnkn_jssk_chks_rieki_2_tskm,
                       total_ssnkn_jssk_zaiko_urg_3_tskm,
                       total_ssnkn_jssk_chks_urg_3_tskm,
                       total_ssnkn_jssk_zaiko_rieki_3_tskm,
                       total_ssnkn_jssk_chks_rieki_3_tskm,
                       total_ssnkn_jssk_zaiko_urg_4_tskm,
                       total_ssnkn_jssk_chks_urg_4_tskm,
                       total_ssnkn_jssk_zaiko_rieki_4_tskm,
                       total_ssnkn_jssk_chks_rieki_4_tskm,
                       total_ssnkn_jssk_zaiko_urg_5_tskm,
                       total_ssnkn_jssk_chks_urg_5_tskm,
                       total_ssnkn_jssk_zaiko_rieki_5_tskm,
                       total_ssnkn_jssk_chks_rieki_5_tskm,
                       total_ssnkn_jssk_zaiko_urg_6_tskm,
                       total_ssnkn_jssk_chks_urg_6_tskm,
                       total_ssnkn_jssk_zaiko_rieki_6_tskm,
                       total_ssnkn_jssk_chks_rieki_6_tskm,
                       total_ssnkn_jssk_zaiko_urg_7_tskm,
                       total_ssnkn_jssk_chks_urg_7_tskm,
                       total_ssnkn_jssk_zaiko_rieki_7_tskm,
                       total_ssnkn_jssk_chks_rieki_7_tskm,
                       total_ssnkn_jssk_zaiko_urg_8_tskm,
                       total_ssnkn_jssk_chks_urg_8_tskm,
                       total_ssnkn_jssk_zaiko_rieki_8_tskm,
                       total_ssnkn_jssk_chks_rieki_8_tskm,
                       total_ssnkn_jssk_zaiko_urg_9_tskm,
                       total_ssnkn_jssk_chks_urg_9_tskm,
                       total_ssnkn_jssk_zaiko_rieki_9_tskm,
                       total_ssnkn_jssk_chks_rieki_9_tskm,
                       total_jssk_mtsh_zaiko_urg_10_tskm,
                       total_jssk_mtsh_chks_urg_10_tskm,
                       total_jssk_mtsh_zaiko_rieki_10_tskm,
                       total_jssk_mtsh_chks_rieki_10_tskm,
                       total_jssk_mtsh_zaiko_urg_11_tskm,
                       total_jssk_mtsh_chks_urg_11_tskm,
                       total_jssk_mtsh_zaiko_rieki_11_tskm,
                       total_jssk_mtsh_chks_rieki_11_tskm,
                       total_jssk_mtsh_zaiko_urg_12_tskm,
                       total_jssk_mtsh_chks_urg_12_tskm,
                       total_jssk_mtsh_zaiko_rieki_12_tskm,
                       total_jssk_mtsh_chks_rieki_12_tskm
                  )>= 1
                  ORDER BY groupby_data.total_all_ssnkn_tncd_kkk_urg_kingaku DESC,groupby_data.kigyo_code, groupby_data.group_code, groupby_data.ctgry_code
                """;
        String fullSqlTemplate = sql1 + sql2;
        return String.format(fullSqlTemplate, condition, group_code, group_code, group_code, condition, dataKubun);
    }
}
