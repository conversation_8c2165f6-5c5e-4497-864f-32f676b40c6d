package com.ms.bp.domain.file.headofficeoutlookplan.strategy;

import com.ms.bp.application.data.FileExportOrchestrator;
import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.model.OutlookPlanInfo;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.repository.impl.AreaCodeRepositoryImpl;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.application.data.FileExportOrchestrator.ExportTask;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * 本社 ファイル分割戦略インターフェース
 * エクスポート時のファイル分割ロジックを定義
 */
public class HeadOfficeOutlookPlanFileSplitStrategy implements FileSplitStrategy {
    private static final Logger logger = LoggerFactory.getLogger(HeadOfficeOutlookPlanFileSplitStrategy.class);

    /**
     * エクスポートタスクを作成
     *
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return エクスポートタスクリスト
     */
    @Override
    public List<FileExportOrchestrator.ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("本社_移管_エクスポートタスク作成開始: ユーザー={}", userInfo.getShainCode());

        // エリア名短縮漢字取得
        OutlookPlanInfo outlookPlanInfo = processAreaCode(exportRequest);

        logger.info("本社_移管_エリア別分割タスク作成開始: エリア={}, データ区分={}, ユーザー={}", outlookPlanInfo.getAreaList(), outlookPlanInfo.getDataKubun(), userInfo.getShainCode());

        List<ExportTask> tasks = outlookPlanInfo.getAreaList().stream()
                .flatMap(area -> {
                    String areaName = area.getAreaName();
                    if (areaName == null || areaName.trim().isEmpty()) {
                        logger.error(formatMessage(GlobalMessageConstants.ERR_012, "エリア名短縮漢字","エリアコード="+area.getAreaCode(),"エリアコードが不存在或いはエリア名短縮漢字が設定されない"));
                        return Stream.empty();
                    }

                    return outlookPlanInfo.getDataKubun().stream()
                                    .flatMap(dataKubun -> {
                                        String prefix = dataKubun.equals(BusinessConstants.DATAKUBUN_IKO_BEFORE) ? BusinessConstants.IKO_BEFORE : BusinessConstants.IKO_AFTER;

                                        // C別
                                        ExportRequest requestC = new ExportRequest();
                                        requestC.setArea(Collections.singletonList(area.getAreaCode()));
                                        requestC.setDataKubun(Collections.singletonList(dataKubun));
                                        String fileNameC = generateAreaFileName(String.format("%s_%s", prefix, BusinessConstants.MTSH_KKK_C), area);
                                        FileExportOrchestrator.ExportTask taskC = new ExportTask(BusinessConstants.MTSH_KKK_FILE_TYPE_C, requestC, BusinessConstants.HEAD_OFFICE_OUTLOOK_PLAN_C, fileNameC,prefix+"_見通し・計画_採算管理単位C別_本社");

                                        // 企業別
                                        ExportRequest requestK = new ExportRequest();
                                        requestK.setArea(Collections.singletonList(area.getAreaCode()));
                                        requestK.setDataKubun(Collections.singletonList(dataKubun));
                                        String fileNameK = generateAreaFileName(String.format("%s_%s", prefix, BusinessConstants.MTSH_KKK_K), area);
                                        FileExportOrchestrator.ExportTask taskK = new ExportTask(BusinessConstants.MTSH_KKK_FILE_TYPE_K, requestK, BusinessConstants.HEAD_OFFICE_OUTLOOK_PLAN_K, fileNameK,prefix+"_見通し・計画_企業別_本社");

                                        return Stream.of(taskC, taskK);
                                    });
                        }
                )
                .collect(Collectors.toList());

        logger.info("本社_移管_エリア別分割タスク作成完了: タスク数={}, ユーザー={}", tasks.size(), userInfo.getShainCode());
        return tasks;
    }

    /**
     * エリア別ファイル名を生成
     *
     * @param prefix 接頭辞
     * @param area エリアコード
     * @return SQLクエリとパラメータ
     */
    private String generateAreaFileName(String prefix,AreaInfo area) {
        return String.format("%s_%s_%s.csv", prefix, area.getAreaName(), DateUtil.getCurrentDateTimeString_YYYYMMDDHHMM());
    }

    /**
     * エリア名短縮漢字取得
     * SKSAが含まれている場合、データベースから実際のエリアコードを取得して置換
     *
     * @param originalRequest 元のエクスポートリクエスト
     * @return 処理済みエクスポートリクエスト
     */
    private OutlookPlanInfo processAreaCode(ExportRequest originalRequest) {
        List<String> originalAreaList = originalRequest.getArea();

        // データベースから実際のエリアコードを取得（DateUtilから次年度を取得）
        List<AreaInfo> actualAreaInfos = LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
            AreaCodeRepositoryImpl areaCodeRepository = new AreaCodeRepositoryImpl(jdbcTemplate);
            return areaCodeRepository.findAreaInfosForMsth(
                    DateUtil.getNextFiscalYear(),
                    originalAreaList.contains(BusinessConstants.PROFIT_MANAGEMENT_PLANNING_AREA_CODE)?BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE:"",
                    originalAreaList);
        });

        // 新しいDtoを作成
        OutlookPlanInfo outlookPlanInfo = new OutlookPlanInfo();
        outlookPlanInfo.setDataType(originalRequest.getDataType());
        outlookPlanInfo.setAreaList(actualAreaInfos);
        outlookPlanInfo.setHnshBashoKubun(originalRequest.getHnshBashoKubun());
        outlookPlanInfo.setDataKubun(originalRequest.getDataKubun());

        return outlookPlanInfo;
    }
}
