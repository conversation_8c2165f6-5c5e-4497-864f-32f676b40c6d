package com.ms.bp.domain.file.planmaster.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 次年度計画マスタデータ取得SQL戦略
 * 複雑なJOINクエリを使用して次年度計画マスタの全データを取得
 * nendo_paramとarea_code_paramの2つのパラメータを使用
 */
public class PlanMasterDataStrategy implements DataAccessStrategy {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterDataStrategy.class);

    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("次年度計画マスタSQL構築開始: ユーザー={}", userInfo.getShainCode());

        // 年度パラメータの設定（DateUtilを使用して次年度を取得）
        String nendoParam = DateUtil.getNextFiscalYear();

        // エリアコードリストの設定
        List<String> areaCodeList = exportRequest.getArea();

        // SQLクエリとパラメータリストを構築
        SqlWithParams queryResult = buildPlanMasterQueryWithParams(nendoParam, areaCodeList);

        logger.info("次年度計画マスタSQL構築完了: 年度={}, エリア数={}", nendoParam, areaCodeList.size());
        return queryResult;
    }

    @Override
    public String getStrategyName() {
        return BusinessConstants.PLAN_MASTER_DEFAULT;
    }

    /**
     * 位置パラメータを使用したSQLクエリとパラメータマップを構築
     * エリアコードリストに基づいてIN句の?プレースホルダーを動的生成
     * パラメータ配列の順序を一致させる必要がある
     */
    private SqlWithParams buildPlanMasterQueryWithParams(String nendoParam, List<String> areaCodeList) {
        // パラメータリストを作成（順序重要）

        // 2. エリアコードパラメータを追加
        String areaCondition;
        String areaCondition1;
        String areaCondition2;
        // エリアコードの数だけ?プレースホルダーを生成
        String areaPlaceholders = String.join(",", Collections.nCopies(areaCodeList.size(), "?"));
        areaCondition = " j.TKY_AREA_CODE IN (" + areaPlaceholders + ") ";
        areaCondition1 = " g.AREA_CODE IN (" + areaPlaceholders + ") ";
        areaCondition2 = " j2.TKY_AREA_CODE IN (" + areaPlaceholders + ") ";
        // エリアコード回追加
        List<Object> paramList = new ArrayList<>();
        paramList.add(nendoParam);
        paramList.add(areaCodeList);
        paramList.add(areaCodeList);
        paramList.add(nendoParam);
        paramList.add(areaCodeList);
        paramList.add(String.valueOf(Integer.parseInt(nendoParam) - 1));
        paramList.add(String.valueOf(Integer.parseInt(nendoParam) - 1));
        paramList.add(nendoParam);
        // SQLクエリを構築
        String sql = buildPlanMasterQueryWithPositionalParams(areaCondition,areaCondition1,areaCondition2);

        // 順序保持のためLinkedHashMapを使用してパラメータマップを構築
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < paramList.size(); i++) {
            paramMap.put("param" + i, paramList.get(i));
        }

        logger.debug("構築されたパラメータ順序: {}", paramList);

        return new SqlWithParams(sql, paramMap);
    }

    /**
     * 次年度計画マスタ取得用SQLクエリを構築
     */
    private String buildPlanMasterQueryWithPositionalParams(String areaCondition,String areaCondition1,String areaCondition2) {
        return String.format("""
                    SELECT
                            jk.AREA_CODE AS area_cd,
                            area_name.AREA_MEI_KANJI AS area_mei,
                            jk.CTGRY_CODE AS cate_cd,
                            category_name.CTGRY_MEI_KANJI AS category,
                            jk.GROUP_CODE AS group_cd,
                            jk.UNIT_CODE AS unit_cd,
                            jk.TNTSH_MEI AS tantousha,
                            jk.KIGYO_CODE AS kigyo_cd,
                            company_name.KGYM_KANJI AS kigyo_mei,
                            jk.GYT_SHK_NO AS gyoutai_shukei,
                            jk.GYTM_KANJI AS gyoutai_mei,
                            jk.SUB_CTGRY_CODE AS sub_category_cd,
                            sub_category_name.SUB_CTGRY_MEI_KANJI AS sub_category_mei,
                            jk.SSNKN_TNCD AS saisan_kanri_tani_cd,
                            jk.SSN_KANRI_TNM_KANJI AS saisan_kanri_tani_mei,
                            attr_name.JIYU_SHYK_MEI_1 AS henkou_mae_torikumi_kubun,
                            jk.HNKG_TRKM_KUBUN AS henkou_go_torikumi_kubun,
                            jk.IKNSK_AREA_CODE AS ikan_saki_area_cd,
                            transfer_area_name.AREA_MEI_KANJI AS ikan_saki_area_mei,
                            jk.IKNSK_GROUP_CODE AS ikan_saki_group_cd,
                            jk.IKNSK_UNIT_CODE AS ikan_saki_unit_cd,
                            jisseki.jisseki_sum AS tounen_jisseki_ruikei,
                            keikaku.keikaku_sum AS tounen_keikaku_ruikei
                        FROM (
                            -- 手順①: 次年度計画マスタから抽出
                            SELECT
                                j.AREA_CODE,
                                j.CTGRY_CODE,
                                j.SUB_CTGRY_CODE,
                                j.GROUP_CODE,
                                j.UNIT_CODE,
                                j.TNTSH_MEI,
                                j.KIGYO_CODE,
                                j.GYT_SHK_NO,
                                j.GYTM_KANJI,
                                j.SSNKN_TNCD,
                                j.SSN_KANRI_TNM_KANJI,
                                j.HNKG_TRKM_KUBUN,
                                j.IKNSK_AREA_CODE,
                                j.IKNSK_GROUP_CODE,
                                j.IKNSK_UNIT_CODE
                            FROM T_JINENDO_KKK j
                            WHERE j.NENDO = ?
                              AND %s
         
                            UNION
            
                            -- 手順②: 共通マスタから抽出
                            SELECT
                                g.AREA_CODE,
                                s.CTGRY_CODE,
                                s.SUB_CTGRY_CODE,
                                g.GROUP_CODE,
                                s.UNIT_CODE,
                                tantou.TNTSH_MEI_KANJI AS TNTSH_MEI,
                                s.KIGYO_CODE,
                                gyt_kanri.GYT_SHK_NO,
                                gyt_mei.GYT_MEI AS GYTM_KANJI,
                                s.SSNKN_TNCD,
                                s.SSN_KANRI_TNM_KANJI,
                                NULL AS HNKG_TRKM_KUBUN,
                                NULL AS IKNSK_AREA_CODE,
                                NULL AS IKNSK_GROUP_CODE,
                                NULL AS IKNSK_UNIT_CODE
                            FROM M_SAISANKANRITANIMST s
                            INNER JOIN M_UNITMST u
                                ON s.UNIT_CODE = u.UNIT_CODE
                                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= u.KSHB
                                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= u.SHRYB
                                AND u.SHIYO_KNSH_KUBUN = '0'
                            INNER JOIN M_GROUPMST g
                                ON u.GROUP_CODE = g.GROUP_CODE
                                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= g.KSHB
                                AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= g.SHRYB
                                AND g.SHIYO_KNSH_KUBUN = '0'
                            -- 担当者マスタ
                            LEFT JOIN LATERAL (
                                SELECT TNTSH_MEI_KANJI
                                FROM M_TANTOSHAMST
                                WHERE TNTSH_CODE = s.TNTSH_CODE
                                  AND UNIT_CODE = s.UNIT_CODE
                                  AND SHIYO_KNSH_KUBUN = '0'
                                ORDER BY UNIT_CODE, TNTSH_CODE
                                LIMIT 1
                            ) tantou ON true
                            -- 業態管理マスタ
                            LEFT JOIN LATERAL (
                                SELECT GYT_SHK_NO
                                FROM M_GYT_KANRI
                                WHERE GYT_CODE = s.GYT_CODE
                                  AND SMK_CODE = s.SMK_CODE
                                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
                                  AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
                                ORDER BY KSHB DESC
                                LIMIT 1
                            ) gyt_kanri ON true
                            -- 業態名マスタ
                            LEFT JOIN LATERAL (
                                SELECT GYT_MEI
                                FROM M_GYT_MEI
                                WHERE GYT_SHK_NO = gyt_kanri.GYT_SHK_NO
                                ORDER BY GYT_SHK_NO
                                LIMIT 1
                            ) gyt_mei ON true
                            WHERE TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= s.KSHB
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= s.SHRYB
                              AND s.SHIYO_KNSH_KUBUN = '0'
                              AND %s
                              AND NOT EXISTS (
                                  SELECT 1
                                  FROM T_JINENDO_KKK j2
                                  WHERE j2.SSNKN_TNCD = s.SSNKN_TNCD
                                    AND j2.NENDO = ?
                                    AND %s
                              )
                        ) jk
                        -- エリア名
                        LEFT JOIN LATERAL (
                            SELECT AREA_MEI_KANJI
                            FROM M_SOSHIKIAREAMST
                            WHERE AREA_CODE = jk.AREA_CODE
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
                              AND SHIYO_KNSH_KUBUN = '0'
                            ORDER BY KSHB DESC
                            LIMIT 1
                        ) area_name ON true
                        -- カテゴリ名
                        LEFT JOIN LATERAL (
                            SELECT CTGRY_MEI_KANJI
                            FROM M_CATEGORYMST
                            WHERE CTGRY_CODE = jk.CTGRY_CODE
                              AND SUB_CTGRY_CODE = jk.SUB_CTGRY_CODE
                              AND SHIYO_KNSH_KUBUN = '0'
                            ORDER BY CTGRY_CODE, SUB_CTGRY_CODE
                            LIMIT 1
                        ) category_name ON true
                        -- サブカテゴリ名
                        LEFT JOIN LATERAL (
                            SELECT SUB_CTGRY_MEI_KANJI
                            FROM M_CATEGORYMST
                            WHERE CTGRY_CODE = jk.CTGRY_CODE
                              AND SUB_CTGRY_CODE = jk.SUB_CTGRY_CODE
                              AND SHIYO_KNSH_KUBUN = '0'
                            ORDER BY CTGRY_CODE, SUB_CTGRY_CODE
                            LIMIT 1
                        ) sub_category_name ON true
                        -- 企業名
                        LEFT JOIN LATERAL (
                            SELECT KGYM_KANJI
                            FROM M_KIGYOMST
                            WHERE KIGYO_CODE = jk.KIGYO_CODE
                              AND SHIYO_KNSH_KUBUN = '0'
                            ORDER BY KIGYO_CODE
                            LIMIT 1
                        ) company_name ON true
                        -- 変更前取組区分(採算管理単位付属項目マスタ)
                        LEFT JOIN LATERAL (
                            SELECT JIYU_SHYK_MEI_1
                            FROM M_SAISANKANRITANIFZKKMKMST
                            WHERE SSNKN_TNCD = jk.SSNKN_TNCD
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
                            ORDER BY KSHB DESC
                            LIMIT 1
                        ) attr_name ON true
                        -- 移管先エリア名
                        LEFT JOIN LATERAL (
                            SELECT AREA_MEI_KANJI
                            FROM M_SOSHIKIAREAMST
                            WHERE AREA_CODE = jk.IKNSK_AREA_CODE
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
                              AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
                              AND SHIYO_KNSH_KUBUN = '0'
                            ORDER BY KSHB DESC
                            LIMIT 1
                        ) transfer_area_name ON true
                        -- 当年度計画累計を計算
                        LEFT JOIN LATERAL (
                            SELECT SUM(kkk_gk) AS keikaku_sum
                            FROM SSNKN_TN_C_CHKST_KKK
                            WHERE kanri_kk_nendo = ?
                              AND ssnkn_tncd = jk.SSNKN_TNCD
                              AND togo__kubun IN ('01', '02')
                              AND (jk.SSNKN_TNCD <> '1118888' OR group_code = jk.GROUP_CODE)
                        ) keikaku ON true
                        -- 当年度実績累計を計算
                        LEFT JOIN LATERAL (
                            SELECT SUM(jssk_gk) AS jisseki_sum
                            FROM SSNKN_TN_C_CHKST_JSSK
                            WHERE kanri_kk_nendo = ?
                              AND ssnkn_tncd = jk.SSNKN_TNCD
                              AND togo__kubun IN ('01', '02')
                              AND (jk.SSNKN_TNCD <> '1118888' OR group_code = jk.GROUP_CODE)
                        ) jisseki ON true
                        -- エリア表示順を取得（ソート用）
                        LEFT JOIN LATERAL (
                            SELECT AREA_ORDER
                            FROM M_SK_KKK_AREA
                            WHERE NENDO = ?
                              AND AREA_CODE = jk.AREA_CODE
                        ) area_sort ON true
                        ORDER BY
                            area_sort.area_order ASC NULLS LAST,
                            jk.GROUP_CODE,
                            jk.UNIT_CODE,
                            jk.SSNKN_TNCD
            """, areaCondition, areaCondition1, areaCondition2);
    }
}
