package com.ms.bp.domain.file.model;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.shared.common.constants.MishuKubun;
import com.ms.bp.shared.common.constants.Zaikokubun;
import com.ms.bp.shared.common.io.validation.annotation.*;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.common.constants.BusinessConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 間接利益メーカーインポートデータ領域モデル
 * ファイル処理領域における間接利益メーカーデータのインポート処理を担当
 * 
 * 主な機能：
 * 1. CSVデータの検証（必須チェック、桁数チェック、書式チェック）
 * 2. 月次データ（4月〜3月）の処理
 * 3. データベースフィールドへの自動マッピング
 */
@Data
public class IndirectProfitMakerImportData implements DatabaseMappable {
    
    // ==================== CSVフィールド（必須項目） ====================
    /**
     * カテゴリコード
     */
    @Required(fieldName = "カテゴリコード")
    @Range(min = 1, max = 1, fieldName = "カテゴリコード")
    @NumericHalfWidth(fieldName = "カテゴリコード")
    @FixedValues(values ={"1", "2", "3", "4"}, fieldName = "カテゴリコード")
    private String categoryCode;
    
    /**
     * 本社場所区分
     */
    @Required(fieldName = "本社場所区分")
    @Range(min = 1, max = 1, fieldName = "本社場所区分")
    @NumericHalfWidth(fieldName = "本社場所区分")
    private String honshaBashoKubun;
    
    /**
     * メーカーコード
     */
    @Required(fieldName = "メーカーコード")
    @Range(min = 7, max = 7, fieldName = "メーカーコード")
    @NumericHalfWidth(fieldName = "メーカーコード")
    private String makerCode;
    
    /**
     * メーカー別管理No.
     */
    @Required(fieldName = "メーカー別管理No.")
    @Range(min = 1, max = 4, fieldName = "メーカー別管理No.")
    @NumericHalfWidth(fieldName = "メーカー別管理No.")
    private String makerKanriNo;

    /**
     * 採算管理単位コード
     */
    @Range(min = 7, max = 7, fieldName = "採算管理単位コード")
    @NumericHalfWidth(fieldName = "採算管理単位コード")
    private String saisaknKanriTaniCode;

    /**
     * 会社コード
     */
    @Range(min = 6, max = 6, fieldName = "会社コード")
    @NumericHalfWidth(fieldName = "会社コード")
    private String kaishyaCode;

    /**
     * エリアコード
     */
    @Range(min = 4, max = 4, fieldName = "エリアコード")
    @NumericHalfWidth(fieldName = "エリアコード")
    private String areaCode;

    /**
     * サブエリアコード
     */
    @Range(min = 4, max = 4, fieldName = "サブエリアコード")
    @NumericHalfWidth(fieldName = "サブエリアコード")
    private String subAreaCode;

    /**
     * グループコード
     */
    @Required(fieldName = "グループコード")
    @Range(min = 4, max = 4, fieldName = "グループコード")
    @NumericHalfWidth(fieldName = "グループコード")
    private String groupCode;
    
    /**
     * 未収区分
     */
    @Required(fieldName = "未収区分")
    @Range(min = 1, max = 1, fieldName = "未収区分")
    @NumericHalfWidth(fieldName = "未収区分")
    @FixedValues(values ={"1", "2", "3", "4"}, fieldName = "未収区分")
    private String mishuKubun;
    
    /**
     * 在直区分
     */
    @Required(fieldName = "在直区分")
    @Range(min = 1, max = 1, fieldName = "在直区分")
    @NumericHalfWidth(fieldName = "在直区分")
    @FixedValues(values ={"1", "3"}, fieldName = "在直区分")
    private String zaichokuKubun;

    // ==================== 月次データ（4月〜3月） ====================
    
    /**
     * 4月
     */
    @Range(min = 1, max = 10, fieldName = "4月")
    @NumericHalfWidth(fieldName = "4月")
    private String month04;
    
    /**
     * 5月
     */
    @Range(min = 1, max = 10, fieldName = "5月")
    @NumericHalfWidth(fieldName = "5月")
    private String month05;
    
    /**
     * 6月
     */
    @Range(min = 1, max = 10, fieldName = "6月")
    @NumericHalfWidth(fieldName = "6月")
    private String month06;
    
    /**
     * 7月
     */
    @Range(min = 1, max = 10, fieldName = "7月")
    @NumericHalfWidth(fieldName = "7月")
    private String month07;
    
    /**
     * 8月
     */
    @Range(min = 1, max = 10, fieldName = "8月")
    @NumericHalfWidth(fieldName = "8月")
    private String month08;
    
    /**
     * 9月
     */
    @Range(min = 1, max = 10, fieldName = "9月")
    @NumericHalfWidth(fieldName = "9月")
    private String month09;
    
    /**
     * 10月
     */
    @Range(min = 1, max = 10, fieldName = "10月")
    @NumericHalfWidth(fieldName = "10月")
    private String month10;
    
    /**
     * 11月
     */
    @Range(min = 1, max = 10, fieldName = "11月")
    @NumericHalfWidth(fieldName = "11月")
    private String month11;
    
    /**
     * 12月
     */
    @Range(min = 1, max = 10, fieldName = "12月")
    @NumericHalfWidth(fieldName = "12月")
    private String month12;
    
    /**
     * 1月
     */
    @Range(min = 1, max = 10, fieldName = "1月")
    @NumericHalfWidth(fieldName = "1月")
    private String month01;
    
    /**
     * 2月
     */
    @Range(min = 1, max = 10, fieldName = "2月")
    @NumericHalfWidth(fieldName = "2月")
    private String month02;
    
    /**
     * 3月
     */
    @Range(min = 1, max = 10, fieldName = "3月")
    @NumericHalfWidth(fieldName = "3月")
    private String month03;
    
    // ==================== 別のフィールド====================

    /**
     * 年度
     */
    private String nendo;

    /**
     * 出力対象
     */
    private String outputTarget;

    /**
     * カテゴリ名（参考用）
     */
    private String categoryName;
    
    /**
     * 本社場所（参考用）
     */
    private String honshaBasho;
    
    /**
     * メーカー名（参考用）
     */
    private String makerName;
    
    /**
     * 採算管理単位名（参考用）
     */
    private String saisaknKanriTaniName;
    
    /**
     * 会社名（参考用）
     */
    private String kaishyaName;
    
    /**
     * エリアコード名（参考用）
     */
    private String areaCodeName;
    
    /**
     * サブエリア名（参考用）
     */
    private String subAreaName;
    
    /**
     * グループ名（参考用）
     */
    private String groupName;

    /**
     * 未収名（参考用）
     */
    private String mishuName;
    
    /**
     * 在庫直送（参考用）
     */
    private String zaikoChokuso;
    
    // ==================== 業務ロジックメソッド ====================
    /**
     * 指定月のデータ値を取得
     * @param month 月（"01"〜"12"形式）
     * @return 指定月のデータ値（文字列）
     */
    public String getMonthData(String month) {
        switch (month) {
            case "01": return month01;
            case "02": return month02;
            case "03": return month03;
            case "04": return month04;
            case "05": return month05;
            case "06": return month06;
            case "07": return month07;
            case "08": return month08;
            case "09": return month09;
            case "10": return month10;
            case "11": return month11;
            case "12": return month12;
            default: return null;
        }
    }

    /**
     * 指定月のデータ値をlong変換後に1000倍してBigDecimalで取得
     * 値が存在する場合のみlong変換を行い、その後1000倍する
     * 
     * @param month 月（"01"〜"12"形式）
     * @return 指定月のデータ値をlong変換後1000倍したBigDecimal（値がない場合は0）
     */
    public BigDecimal getMonthDataAsScaledBigDecimal(String month) {
        String value = getMonthData(month);
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            // 文字列をlongに変換してから1000倍
            long longValue = Long.parseLong(value.trim());
            return BigDecimal.valueOf(longValue * 1000L);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
    
    // ==================== DatabaseMappable実装 ====================
    
    /**
     * DTOの核心フィールドをデータベースフィールドのMapに変換
     * 間接利益メーカー表のフィールドにマッピング
     * 固定値フィールドの注入は親インターフェースのテンプレートメソッドで統一処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @param fields データベースフィールドのMap（固定値フィールドは含まない）
     */
    @Override
    public void toDatabaseFieldsCore(boolean isInsert, Map<String, Object> fields) {
        // 年度（次年度を自動設定）
        fields.put("NENDO", DateUtil.getNextFiscalYear());
        fields.put("MAKER_CODE", makerCode);
        fields.put("MAKER__KANRI_NO", BigDecimal.valueOf(Long.parseLong(makerKanriNo)));
        fields.put("CTGRY_CODE", categoryCode);
        fields.put("GROUP_CODE", groupCode);
        fields.put("HNSH_BASHO_KUBUN", honshaBashoKubun);
        fields.put("ZAICHOKU_KUBUN", zaichokuKubun);
        fields.put("MISHU_KUBUN", mishuKubun);

        fields.put("SSNKN_TNCD", saisaknKanriTaniCode);
        fields.put("SYSTM_UNYO_KIGYO_CODE", ms_systmUnyoKigyoCode);
        fields.put("AREA_CODE", ms_areaCode);
        fields.put("SUB_AREA_CODE", ms_subAreaCode);

        // 間接利益月次データ
        fields.put("KNSTS_RIEKI_KKK_1_TSKM", getMonthDataAsScaledBigDecimal("04"));
        fields.put("KNSTS_RIEKI_KKK_2_TSKM", getMonthDataAsScaledBigDecimal("05"));
        fields.put("KNSTS_RIEKI_KKK_3_TSKM", getMonthDataAsScaledBigDecimal("06"));
        fields.put("KNSTS_RIEKI_KKK_4_TSKM", getMonthDataAsScaledBigDecimal("07"));
        fields.put("KNSTS_RIEKI_KKK_5_TSKM", getMonthDataAsScaledBigDecimal("08"));
        fields.put("KNSTS_RIEKI_KKK_6_TSKM", getMonthDataAsScaledBigDecimal("09"));
        fields.put("KNSTS_RIEKI_KKK_7_TSKM", getMonthDataAsScaledBigDecimal("10"));
        fields.put("KNSTS_RIEKI_KKK_8_TSKM", getMonthDataAsScaledBigDecimal("11"));
        fields.put("KNSTS_RIEKI_KKK_9_TSKM", getMonthDataAsScaledBigDecimal("12"));
        fields.put("KNSTS_RIEKI_KKK_10_TSKM", getMonthDataAsScaledBigDecimal("01"));
        fields.put("KNSTS_RIEKI_KKK_11_TSKM", getMonthDataAsScaledBigDecimal("02"));
        fields.put("KNSTS_RIEKI_KKK_12_TSKM", getMonthDataAsScaledBigDecimal("03"));

        // 名称系
        fields.put("SSN_KANRI_TNM_KANJI", ms_ssnKanriTnmKanji);
        fields.put("SYSTM_UNYO_KGYM_KANJI", ms_systmUnyoKgymKanji);
        fields.put("AREA_MEI_KANJI", ms_areaMeiKanji);
        fields.put("SUB_AREA_MEI_KANJI", ms_subAreaMeiKanji);
        fields.put("GROUP_MEI_KANJI", ms_groupMeiKanji);
        fields.put("MISHU_MEI", MishuKubun.getByCode(Integer.parseInt(mishuKubun)).getText());
        fields.put("ZAIKO_CHKS", Zaikokubun.getByCode(Integer.parseInt(zaichokuKubun)).getText());

        // プログラムIDをFunctionUtilから取得
        String programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE).getFunctionId();

        // 自動生成フィールド
        Date currentTime = new Date();
        String currentTimeStr = DateUtil.formatDateTime(currentTime);

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            fields.put("TRK_PRGRM_ID", programId);
            fields.put("VRSN", 1);
            fields.put("RCRD_TRK_NCHJ", currentTimeStr);
        } else {
            fields.remove("TRK_SYSTM_UNYO_KIGYO_CODE");
            fields.remove("TRK_SHAIN_CODE");
        }

        // 更新時は常に設定
        fields.put("KSHN_PRGRM_ID", programId);
        fields.put("RCRD_KSHN_NCHJ", currentTimeStr);
    }

    // ==================== マスタデータ補完機能実装 ====================
    // ==================== マスタデータ補完フィールド ====================
    /**
     * 採算管理単位名漢字（マスタデータから補完）
     */
    private String ms_ssnKanriTnmKanji;
    /**
     * システム運用企業コード（マスタデータから補完）
     */
    private String ms_systmUnyoKigyoCode;
    /**
     * システム運用企業名漢字（マスタデータから補完）
     */
    private String ms_systmUnyoKgymKanji;
    /**
     * エリアコード（マスタデータから補完）
     */
    private String ms_areaCode;
    /**
     * エリア名漢字（マスタデータから補完）
     */
    private String ms_areaMeiKanji;
    /**
     * サブエリアコード（マスタデータから補完）
     */
    private String ms_subAreaCode;
    /**
     * サブエリア名漢字（マスタデータから補完）
     */
    private String ms_subAreaMeiKanji;
    /**
     * グループ名漢字（マスタデータから補完）
     */
    private String ms_groupMeiKanji;

    @Override
    public boolean requiresMasterDataEnrichment() {
        return true; // 次年度計画マスタはマスタデータ補完が必要
    }

    @Override
    public void applyMasterData(Map<String, Object> masterDataMap) {
        if (masterDataMap == null || masterDataMap.isEmpty()) {
            return;
        }

        //採算管理単位名設定
        String key=String.join("_", groupCode, saisaknKanriTaniCode);
        if (masterDataMap.containsKey(key) && masterDataMap.get(key) != null) {
            var obj = (GroupMasterInfo) masterDataMap.get(key);
            this.ms_ssnKanriTnmKanji = obj.getSsnKanriTnmKanji();
            this.ms_systmUnyoKigyoCode = obj.getSystmUnyoKigyoCode();
            this.ms_systmUnyoKgymKanji = obj.getSystmUnyoKgymKanji();
            this.ms_areaCode = obj.getAreaCode();
            this.ms_areaMeiKanji = obj.getAreaMeiKanji();
            this.ms_subAreaCode = obj.getSubAreaCode();
            this.ms_subAreaMeiKanji = obj.getSubAreaMeiKanji();
            this.ms_groupMeiKanji = obj.getGroupMeiKanji();
        }
    }
}

