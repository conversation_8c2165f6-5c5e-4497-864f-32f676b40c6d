package com.ms.bp.domain.file.indirectprofitmaker;

import com.ms.bp.domain.file.model.IndirectProfitMakerImportData;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.repository.*;
import com.ms.bp.domain.master.service.MasterDataEnrichmentService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.master.model.AreaInfo;
//import com.ms.bp.domain.master.repository.MakerMasterRepository;
import com.ms.bp.infrastructure.repository.impl.*;
//import com.ms.bp.infrastructure.repository.impl.MakerMasterRepositoryImpl;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ExportFileNameUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.shared.util.RequestContext;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * 間接利益メーカー別インポートサービスの実装クラス
 * ファイル処理領域における間接利益メーカー別データのインポート処理を担当
 * 
 * 主な機能：
 * 1. CSVファイルの解析とデータ検証
 * 2. 権限チェック（エリアコード関連）
 * 3. データベース存在性チェック（メーカー、グループ、ユニット等のマスタ）
 * 4. 間接利益メーカー別テーブルへの自動UPSERT処理
 * 5. 月次データ（4月-3月）の処理
 */
public class IndirectProfitMakerImportService extends AbstractImportService<IndirectProfitMakerImportData> {

    private static final Logger logger = LoggerFactory.getLogger(IndirectProfitMakerImportService.class);

    /**
     * コンストラクタ
     */
    public IndirectProfitMakerImportService() {
        logger.debug("IndirectProfitMakerImportServiceが初期化されました");
    }

    @Override
    protected String getDataType() {
        return BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE; // 間接利益メーカー別のデータタイプコード
    }

    @Override
    protected DataValidator getDataValidator() {
        // 領域モデルベースのバリデーターを使用（アノテーション検証 + カスタム検証）
        return new DTODataValidator<>(IndirectProfitMakerImportData.class, this::validateCustomLogic);
    }

    @Override
    protected Class<IndirectProfitMakerImportData> getDTOClass() {
        return IndirectProfitMakerImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        // CSVヘッダーフィールドマッピングを構築
        Map<String, String> headerMapping = new HashMap<>();

        headerMapping.put("年度", "nendo");
        headerMapping.put("出力対象", "outputTarget");
        headerMapping.put("カテゴリコード", "categoryCode");
        headerMapping.put("カテゴリ名", "categoryName"); // 参考用
        headerMapping.put("本社場所区分", "honshaBashoKubun");
        headerMapping.put("本社場所", "honshaBasho"); // 参考用
        headerMapping.put("メーカーコード", "makerCode");
        headerMapping.put("メーカー名", "makerName"); // 参考用
        headerMapping.put("メーカー別管理No.", "makerKanriNo");
        headerMapping.put("採算管理単位コード", "saisaknKanriTaniCode");
        headerMapping.put("採算管理単位名", "saisaknKanriTaniName"); // 参考用
        headerMapping.put("会社コード", "kaishyaCode");
        headerMapping.put("会社名", "kaishyaName"); // 参考用
        headerMapping.put("エリアコード", "areaCode");
        headerMapping.put("エリアコード名", "areaCodeName"); // 参考用
        headerMapping.put("サブエリアコード", "subAreaCode");
        headerMapping.put("サブエリア名", "subAreaName"); // 参考用
        headerMapping.put("グループコード", "groupCode");
        headerMapping.put("グループ名", "groupName"); // 参考用
        headerMapping.put("未収区分", "mishuKubun");
        headerMapping.put("未収名", "mishuName"); // 参考用
        headerMapping.put("在直区分", "zaichokuKubun");
        headerMapping.put("在庫直送", "zaikoChokuso"); // 参考用
        headerMapping.put("4月", "month04");
        headerMapping.put("5月", "month05");
        headerMapping.put("6月", "month06");
        headerMapping.put("7月", "month07");
        headerMapping.put("8月", "month08");
        headerMapping.put("9月", "month09");
        headerMapping.put("10月", "month10");
        headerMapping.put("11月", "month11");
        headerMapping.put("12月", "month12");
        headerMapping.put("1月", "month01");
        headerMapping.put("2月", "month02");
        headerMapping.put("3月", "month03");

        // 固定値フィールドを構築（RequestContextからユーザー情報を取得）
        Map<String, Object> additionalFields = buildAdditionalFields();

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(BusinessConstants.CSV_UPLOAD_BATCH_SIZE)
                .targetTable("T_KNSTS_RIEKI_KKK_MAKER") // 間接利益メーカー別テーブル
                // 複合主キー：年度 + メーカーコード + メーカー別管理No. + カテゴリコード + グループコード + 本社場所区分 + 在直区分
                .keyColumns("NENDO", "MAKER_CODE", "MAKER__KANRI_NO", "CTGRY_CODE", "GROUP_CODE", "HNSH_BASHO_KUBUN", "ZAICHOKU_KUBUN")
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName(ExportFileNameUtil.getFileNameByFileType(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE))
                .enableFieldMapping(true)  // フィールドマッピングを有効化
                .headerFieldMapping(headerMapping)
                .additionalFields(additionalFields)
                .build();
    }

    /**
     * 固定値フィールドを構築（RequestContextからユーザー情報を取得）
     */
    private Map<String, Object> buildAdditionalFields() {
        Map<String, Object> additionalFields = new HashMap<>();

        // RequestContextからユーザー情報を取得
        UserInfo userInfo = RequestContext.getUserInfo();

        if (userInfo != null) {
            // ユーザー情報が取得できた場合、実際の値を設定
            String companyCode = userInfo.getSystemOperationCompanyCode();
            String shainCode = userInfo.getShainCode();

            additionalFields.put("TRK_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SHAIN_CODE", shainCode);
            additionalFields.put("KSHN_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("KSHN_SHAIN_CODE", shainCode);

            logger.debug("ユーザー情報から固定値フィールドを設定: 企業コード={}, 社員コード={}",
                        companyCode, shainCode);
        }

        return additionalFields;
    }

    /**
     * カスタムビジネスロジック検証
     * アノテーション検証では対応できない複雑な検証ロジックを実装
     *
     * 検証内容：
     * 1. 権限チェック（エリアコード関連）
     * 2. データベース存在性チェック（メーカー、グループ、ユニット等のマスタ）
     * 3. 業務ルールに基づく検証（月次データの整合性等）
     *
     * @param data 検証対象のデータ
     * @param options インポートオプション
     * @return 検証エラーのリスト
     */
    protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
        List<ValidationError> errors = new ArrayList<>();

        // RequestContextからユーザー情報を取得して権限チェック
        UserInfo userInfo = RequestContext.getUserInfo();
        if (userInfo != null) {
            validatePermissions(data, userInfo, errors);
        }

        // データベース存在性チェック
        validateDatabaseReferences(data, errors);

        return errors;
    }

    /**
     * 権限チェック処理
     * @param data 検証対象のデータ
     * @param userInfo ユーザ情報
     * @param errors チェックエラー
     */
    private void validatePermissions(Map<String, Object> data, UserInfo userInfo, List<ValidationError> errors) {
        logger.debug("権限チェック開始: ユーザー={}", userInfo.getShainCode());

        // ユーザーに紐づくエリアコードとァイフル内のグループコードが紐づくか
        String groupCode = (String) data.get("groupCode");
        List<AreaInfo> areaInfos = userInfo.getAreaInfos();
        if (hasValue(groupCode) && areaInfos != null && !areaInfos.isEmpty()) {
            // データベース存在性チェック
            validateAreaOfGroupPermission(groupCode, areaInfos, errors);
        }

        // 本社・場所区分のチェック
        String systemAdminFlag = RequestContext.getImportRequest().getSystemAdminFlag();
        String honshaBashoKubun = (String) data.get("honshaBashoKubun");
        List<UserPermissionsResponseV2.PermissionInfo> permissionInfos = userInfo.getPermissionList();
        // システム管理者以外
        if (!systemAdminFlag.equals(BusinessConstants.SYSTEM_ADMIN_FLAG_ADMIN)
                && permissionInfos != null && !permissionInfos.isEmpty()
                && hasValue(honshaBashoKubun)) {
            validateHonshaBashoKubunPermission(permissionInfos,honshaBashoKubun,errors);
        }

        logger.debug("権限チェック完了: エラー数={}", errors.size());
    }

    /**
     * 本社・場所区分のチェック処理
     * @param permissionInfos 権限リスト
     * @param honshaBashoKubun 本社場所区分
     * @param errors チェックエラー
     */
    private void validateHonshaBashoKubunPermission(List<UserPermissionsResponseV2.PermissionInfo> permissionInfos, String honshaBashoKubun, List<ValidationError> errors) {
        //レスポンス.ロールリスト(0).権限リスト.ファイル種別に「間接利益計画_メーカー別」
        permissionInfos.stream()
                .filter(p -> p.getFileType().equals(BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE))
                .findFirst()
                .ifPresent(permission -> {
                    String areaPattern = permission.getAreaPattern();
                    //エリアパターンに「1:エリアパターン1（本社相当権限）」の場合、CSV.本社場所区分が 0:本社であること
                    if (areaPattern.equals(BusinessConstants.AREA_PATTERN_HOSHA)
                            && !honshaBashoKubun.equals(BusinessConstants.HONSHA_KUBUN_CODE)) {
                        errors.add(new ValidationError("本社場所区分", honshaBashoKubun, formatMessage(GlobalMessageConstants.ERR_027, "本社場所区分")));
                    } else if (areaPattern.equals(BusinessConstants.AREA_PATTERN_AERA)
                            && !honshaBashoKubun.equals(BusinessConstants.BASHO_KUBUN_CODE)) {
                        //エリアパターンに「2:エリアパターン2（自エリアのみに権限を持つ）」の場合、CSV.本社場所区分が　1:場所であること
                        errors.add(new ValidationError("本社場所区分", honshaBashoKubun, formatMessage(GlobalMessageConstants.ERR_027, "本社場所区分")));
                    }
                });
    }

    /**
     * エリア権限検証
     * ユーザーに紐づくエリアコードとァイフル内のグループコードが紐づくかを検証する
     *
     * @param groupCode グループコード
     * @param areaInfos アリア情報
     * @param errors チェックエラー
     */
    private void validateAreaOfGroupPermission(String groupCode,  List<AreaInfo> areaInfos, List<ValidationError> errors) {

        try {
            Set<String> allowedAreaCodes = areaInfos.stream()
                    .map(AreaInfo::getAreaCode)
                    .collect(Collectors.toSet());

            // LambdaResourceManagerを使用してデータベース操作を実行
            LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                // Repository インスタンスを作成（キャッシュしない）
                GroupMasterRepository groupMasterRepository = new GroupMasterRepositoryImpl(jdbcTemplate);
                List<GroupMasterInfo> groupInfos = groupMasterRepository.findByGroupCode(groupCode);

                boolean hasValidArea = groupInfos.stream()
                        .map(GroupMasterInfo::getAreaCode)
                        .anyMatch(allowedAreaCodes::contains);

                if (!hasValidArea) {
                    errors.add(new ValidationError("グループコード", groupCode, formatMessage(GlobalMessageConstants.ERR_027, "グループコード")));
                }
                return null; // 戻り値は不要
            });

        } catch (Exception e) {
            logger.error("グループマスタ.エリアコードの存在性チェック中に予期せぬエラーが発生しました", e);
            throw new RuntimeException("グループマスタ.エリアコードの存在性チェック中に予期せぬエラーが発生しました", e);
        }
    }

    /**
     * データベース存在性チェック
     *
     * @param data 検証対象のデータ
     * @param errors チェックエラー
     */
    private void validateDatabaseReferences(Map<String, Object> data, List<ValidationError> errors) {

        try {
            String makerCode = (String) data.get("makerCode");
            String groupCode = (String) data.get("groupCode");
            String saisaknKanriTaniCode = (String) data.get("saisaknKanriTaniCode");

            // LambdaResourceManagerを使用してデータベース操作を実行
            LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {

                if(hasValue(makerCode)) {
                    // Repository インスタンスを作成（キャッシュしない）
                    MakerMasterRepository makerMasterRepository = new MakerMasterRepositoryImpl(jdbcTemplate);

                    // メーカーコードがメーカーマスタに存在するかチェック
                    boolean hasMaker = makerMasterRepository.existsByMakerCode(makerCode);
                    if (!hasMaker) {
                        errors.add(new ValidationError("メーカーコード", makerCode, formatMessage(GlobalMessageConstants.ERR_030, "メーカーコード", makerCode, "メーカーマスタ")));
                    }
                }

                if(hasValue(saisaknKanriTaniCode) && hasValue(groupCode)){
                    //  採算管理単位コード、グループコードがエリア＿見通し・計画_採算管理単位C別、次年度計画マスタに存在するかのチェック
                    // 年度パラメータの設定（DateUtilを使用して次年度を取得）
                    String nendoParam = DateUtil.getNextFiscalYear();
                    JinendoMasterRepository jinendoMasterRepository = new JinendoMasterRepositoryImpl(jdbcTemplate);

                    boolean hasData = jinendoMasterRepository.existsBySnknTncdAndGroupCode(nendoParam,saisaknKanriTaniCode,groupCode);
                    if (!hasData) {
                        errors.add(new ValidationError("採算管理単位コード", saisaknKanriTaniCode, formatMessage(GlobalMessageConstants.ERR_030, "採算管理単位コード",saisaknKanriTaniCode,"エリア＿見通し・計画_採算管理単位C別")));
                    }
                }

                return null; // 戻り値は不要
            });

        } catch (Exception e) {
            logger.error("データベース存在性チェック中に予期せぬエラーが発生しました", e);
            throw new RuntimeException("データベース存在性チェック中に予期せぬエラーが発生しました", e);
        }
    }

    /**
     * 値があるかどうかチェック
     */
    private boolean hasValue(String value) {
        return value != null && !value.trim().isEmpty();
    }

    @Override
    protected void enrichMasterData(List<IndirectProfitMakerImportData> dtos,
                                    ImportSessionCache cache) {

        if (dtos == null || dtos.isEmpty()) return;
        logger.debug("間接利益計画メーカー別のマスタデータ補完開始: 対象DTO数={}", dtos.size());

        try {
            var enrichmentService = new MasterDataEnrichmentService();
            List<GroupSaisanPair> pairsToSearch = dtos.stream()
                    .filter(dto -> dto.getSaisaknKanriTaniCode() != null && !dto.getSaisaknKanriTaniCode().isEmpty())
                    .map(dto -> new GroupSaisanPair(dto.getGroupCode(), dto.getSaisaknKanriTaniCode()))
                    .distinct()
                    .toList();
            //  採算管理単位コード、グループコードがで次年度計画マスタから採算管理単位名漢字を一括取得
            var ssnKanriTnmKanjis = enrichmentService.getJinendoMasterInfos(pairsToSearch, cache);

            List<String> groupCodes = dtos.stream()
                    .map(IndirectProfitMakerImportData::getGroupCode)
                    .distinct()
                    .collect(Collectors.toList());
            //  グループコードでグループマスタに関する情報を一括取得
            var groupMasterInfos = enrichmentService.getGroupMasterInfos(groupCodes, cache);

            // 各DTOにマスタデータを適用
            dtos.forEach(dto -> {
                var masterDataMap = new HashMap<String, Object>();
                var key = String.join("_", dto.getGroupCode(), dto.getSaisaknKanriTaniCode());

                Optional.of(dto.getGroupCode())
                        .filter(groupMasterInfos::containsKey)
                        .ifPresent(groupCode ->
                                masterDataMap.put(key, groupMasterInfos.get(groupCode)));

                Optional.of(key)
                        .filter(ssnKanriTnmKanjis::containsKey)
                        .ifPresent(optionalKey -> {
                            var groupMasterInfoObj = new GroupMasterInfo();
                            if (masterDataMap.get(optionalKey) != null) {
                                groupMasterInfoObj = (GroupMasterInfo) masterDataMap.get(optionalKey);
                            }
                            var newObj= groupMasterInfoObj.toBuilder().ssnKanriTnmKanji(ssnKanriTnmKanjis.get(optionalKey)).build();
                            masterDataMap.put(key, newObj);
                        });

                dto.applyMasterData(masterDataMap);
            });
        } catch (Exception e) {
            String errorMsg = "間接利益計画メーカー別のマスタデータ補完中にエラーが発生しました: " + e.getMessage();
            logger.error(errorMsg, e);
            throw new RuntimeException("間接利益計画メーカー別のマスタデータ補完中にエラーが発生しました", e);
        }
    }

    public record GroupSaisanPair(String groupCode, String ssnknTncd) {}

    public record GroupInfoPair(String groupMeiKanji, String areaCode, String areaMeiKanji, String subAreaCode, String subAreaMeiKanji, String systmUnyoKigyoCode, String systmUnyoKgymKanji) {}
}
