package com.ms.bp.domain.master.service;

import com.ms.bp.domain.file.indirectprofitmaker.IndirectProfitMakerImportService;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.model.JinendoMasterInfo;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.domain.master.repository.JinendoMasterRepository;
import com.ms.bp.domain.master.repository.MakerMasterRepository;
import com.ms.bp.infrastructure.repository.impl.GroupMasterRepositoryImpl;
import com.ms.bp.infrastructure.repository.impl.JinendoMasterRepositoryImpl;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import lombok.Getter;
import java.sql.SQLException;
import java.util.*;

/**
 * マスタデータ補完ドメインサービス
 * インポート処理中のマスタデータ取得と補完を支援するドメイン層サービス
 * 
 * DDD分層原則に従い、domain層に配置
 * 主な機能：
 * - 批量查询优化
 * - キャッシュ機能
 * - エラーハンドリング
 */
public class MasterDataEnrichmentService {
    private static final Logger logger = LoggerFactory.getLogger(MasterDataEnrichmentService.class);

    /**
     * 採算管理単位コード、グループコードがで次年度計画マスタから採算管理単位名漢字を一括取得
     *
     * @param pairsToSearch 検索対象のデータ
     * @param cache セッションキャッシュ
     */
    public HashMap<String, String> getJinendoMasterInfos(List<IndirectProfitMakerImportService.GroupSaisanPair> pairsToSearch, ImportSessionCache cache) {
        // キャッシュから取得とキャッシュ未登録コードの分離
        var resultList = new HashMap<String, String>();
        // 年度パラメータの設定（DateUtilを使用して次年度を取得）
        String nendoParam = DateUtil.getNextFiscalYear();

        var uncachedPairs = pairsToSearch.stream()
                .filter(value -> {
                    var cacheKey = ImportSessionCache.generateKey("INPM_IMPORT", String.join("_", nendoParam, value.groupCode(), value.ssnknTncd()), "");
                    var cachedName = cache.get(cacheKey);
                    if (cachedName != null) {
                        resultList.put(String.join("_", value.groupCode(), value.ssnknTncd()), (String) cachedName);
                        return false; // キャッシュにあるので除外
                    }
                    return true; // キャッシュにないので含める
                })
                .toList();

        // キャッシュにないデータをデータベースから取得
        if (!uncachedPairs.isEmpty()) {
            var dbList = findBySnknTncdAndGroupCode(uncachedPairs, nendoParam);

            // 結果をキャッシュに保存
            dbList.forEach((value) -> {
                var cacheKey = ImportSessionCache.generateKey("INPM_IMPORT", String.join("_", nendoParam, value.getGroupCode(), value.getSsnknTncd()), "");
                cache.put(cacheKey, value.getSsnKanriTnmKanji());
                resultList.put(String.join("_", value.getGroupCode(), value.getSsnknTncd()), value.getSsnKanriTnmKanji());
            });

            logger.debug("採算管理単位名漢字取得完了: DB取得={}, キャッシュ取得={}",
                    dbList.size(), pairsToSearch.size() - uncachedPairs.size());
        }

        return resultList;
    }

    /**
     * グループコードでグループマスタに関する情報を一括取得
     *
     * @param groupCodes 検索対象のデータ
     * @param cache セッションキャッシュ
     */
    public HashMap<String, GroupMasterInfo> getGroupMasterInfos(List<String> groupCodes, ImportSessionCache cache) {
        // キャッシュから取得とキャッシュ未登録コードの分離
        var resultList = new HashMap<String, GroupMasterInfo>();

        var uncachedGroupCodes = groupCodes.stream()
                .filter(value -> {
                    var cacheKey = ImportSessionCache.generateKey("INPM_IMPORT",value, "");
                    var cachedValue = cache.get(cacheKey);
                    if (cachedValue != null) {
                        resultList.put(value, (GroupMasterInfo) cachedValue);
                        return false; // キャッシュにあるので除外
                    }
                    return true; // キャッシュにないので含める
                })
                .toList();

        // キャッシュにないデータをデータベースから取得
        if (!uncachedGroupCodes.isEmpty()) {
            var dbList = findGroupAndAreaInfoByGroupCode(uncachedGroupCodes);

            // 結果をキャッシュに保存
            dbList.forEach((value) -> {
                var cacheKey = ImportSessionCache.generateKey("INPM_IMPORT",value.getGroupCode(), "");
                cache.put(cacheKey, value);
                resultList.put(value.getGroupCode(),value);
            });

            logger.debug("グループ情報とエリア情報とシステム運用企業情報取得完了: DB取得={}, キャッシュ取得={}",
                    dbList.size(), groupCodes.size() - uncachedGroupCodes.size());
        }

        return resultList;
    }

    /**
     * データベース存在性チェック
     *
     * @param pairsToSearch 検索対象のデータ
     * @param nendoParam 検索年度
     */
    private  List<JinendoMasterInfo> findBySnknTncdAndGroupCode(List<IndirectProfitMakerImportService.GroupSaisanPair> pairsToSearch, String nendoParam) {
        List<JinendoMasterInfo> jinendoMasterInfos=new ArrayList<JinendoMasterInfo>();

        try {
            // LambdaResourceManagerを使用してデータベース操作を実行
            jinendoMasterInfos= LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                JinendoMasterRepository jinendoMasterRepository = new JinendoMasterRepositoryImpl(jdbcTemplate);

                return jinendoMasterRepository.findBySnknTncdAndGroupCode(nendoParam,pairsToSearch);
            });

        } catch (Exception e) {
            logger.error("採算管理単位コード、グループコードで次年度計画マスタの情報取得中に予期せぬエラーが発生しました", e);
            throw new RuntimeException("採算管理単位コード、グループコードで次年度計画マスタの情報取得中に予期せぬエラーが発生しました", e);
        }

        return jinendoMasterInfos;
    }

    /**
     * データベース存在性チェック
     *
     * @param groupCodes 検索対象のデータ
     */
    private  List<GroupMasterInfo> findGroupAndAreaInfoByGroupCode(List<String> groupCodes) {
        List<GroupMasterInfo> groupMasterInfo=new ArrayList<GroupMasterInfo>();

        try {
            // LambdaResourceManagerを使用してデータベース操作を実行
            groupMasterInfo= LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                GroupMasterRepository groupMasterRepository = new GroupMasterRepositoryImpl(jdbcTemplate);

                return groupMasterRepository.findGroupAndAreaInfoByGroupCode(groupCodes);
            });

        } catch (Exception e) {
            logger.error("グループ情報とエリア情報とシステム運用企業情報取得中に予期せぬエラーが発生しました", e);
            throw new RuntimeException("グループ情報とエリア情報とシステム運用企業情報取得中に予期せぬエラーが発生しました", e);
        }

        return groupMasterInfo;
    }
}
