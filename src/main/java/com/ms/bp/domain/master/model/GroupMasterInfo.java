package com.ms.bp.domain.master.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * グループマスタ情報値オブジェクト
 * グループマスタから取得するグループ情報を表現する値オブジェクト
 * M_GROUPMST（グループマスタ）の関連データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class GroupMasterInfo {
    
    /**
     * グループコード
     * M_GROUPMST.GROUP_CODE
     */
    private String groupCode;
    
    /**
     * グループ名
     * M_GROUPMST.GROUP_MEI
     */
    private String groupName;

    /**
     * グループ名漢字
     * M_GROUPMST.GROUP_MEI_KANJI
     */
    private String groupMeiKanji;

    /**
     * 引継元ユニットコード
     * M_GROUPMST.HKMT_UNIT_CODE
     */
    private String inheritanceUnitCode;

    /**
     * エリアコード
     * M_GROUPMST.AREA_CODE
     */
    private String areaCode;

    /**
     * サッブエリアコード
     * M_GROUPMST.SUB_AREA_CODE
     */
    private String subAreaCode;

    /**
     * エリア名漢字
     * M_SOSHIKIAREAMST.AREA_MEI_KANJI
     */
    private String areaMeiKanji;

    /**
     * サッブエリア名漢字
     * M_SOSHIKIAREAMST.SUB_AREA_MEI_KANJI
     */
    private String subAreaMeiKanji;

    /**
     * システム運用企業コード
     * M_GROUPMST.SYSTM_UNYO_KIGYO_CODE
     */
    private String systmUnyoKigyoCode;

    /**
     * システム運用企業名漢字
     * M_SYSTEMUNYOKIGYOMST.SYSTM_UNYO_KGYM_KANJI
     */
    private String systmUnyoKgymKanji;

    /**
     * 採算管理単位名漢字
     */
    private String ssnKanriTnmKanji;

    public GroupMasterInfo(String groupCode, String groupMeiKanji, String areaCode, String areaMeiKanji, String subAreaCode, String subAreaMeiKanji, String systmUnyoKigyoCode, String systmUnyoKgymKanji) {
    }
}
