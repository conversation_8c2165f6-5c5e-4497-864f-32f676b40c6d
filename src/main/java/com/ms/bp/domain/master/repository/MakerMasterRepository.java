package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.GroupMasterInfo;

import java.sql.SQLException;
import java.util.List;

/**
 * メーカーマスタリポジトリ
 * メーカーマスタ領域のデータ永続化を抽象化
 * M_MAKERMST（メーカーマスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface MakerMasterRepository {

    /**
     * メーカーコードでメーカーマスタの存在チェック
     *
     * @param makerCode メーカーコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    boolean existsByMakerCode(String makerCode);
}
