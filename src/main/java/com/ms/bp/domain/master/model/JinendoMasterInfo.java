package com.ms.bp.domain.master.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 次年度計画マスタ情報値オブジェクト
 * 次年度計画マスタから取得するグループ情報を表現する値オブジェクト
 * T_JINENDO_KKK（次年度計画マスタ）の関連データを保持
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JinendoMasterInfo {
    /**
     * 年度
     * T_JINENDO_KKK.NENDO
     */
    private String nendo;

    /**
     * グループコード
     * T_JINENDO_KKK.GROUP_CODE
     */
    private String groupCode;

    /**
     * 採算管理単位コード
     * T_JINENDO_KKK.SSNKN_TNCD
     */
    private String ssnknTncd;

    /**
     * 採算管理単位名漢字
     * T_JINENDO_KKK.SSN_KANRI_TNM_KANJI
     */
    private String ssnKanriTnmKanji;

}
